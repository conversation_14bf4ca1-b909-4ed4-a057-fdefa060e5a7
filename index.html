<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Huval - Revolutionary AI Evaluation Platform</title>
    <meta name="description" content="Community-driven AI evaluation platform that breaks away from biased datasets. Create topics, evaluate AI responses, and discover the best performing AI models through real user feedback.">

    <!-- CDN Resources -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" integrity="sha512-DTOQO9RWCH3ppGqcWaEA1BIZOC6xxalwEsw9c2QQeAIftl+Vegovlnee1c9QX4TctnWMn13TZye+giMm8e2LwA==" crossorigin="anonymous" referrerpolicy="no-referrer">

    <!-- Custom Styles -->
    <style>
        :root {
            /* Color System */
            --color-primary: #10B981;
            --color-secondary: #6366F1;
            --color-accent: #F59E0B;
            --color-dark: #0F172A;
            --color-dark-lighter: #1E293B;
            --color-light: #ffffff;
            --color-gray-50: #F8FAFC;
            --color-gray-100: #F1F5F9;
            --color-gray-200: #E2E8F0;
            --color-gray-300: #CBD5E1;
            --color-gray-400: #94A3B8;
            --color-gray-500: #64748B;
            --color-gray-600: #475569;
            --color-gray-700: #334155;
            --color-gray-800: #1E293B;
            --color-gray-900: #0F172A;

            /* Typography */
            --font-primary: 'Inter', system-ui, -apple-system, sans-serif;

            /* Spacing */
            --space-1: 0.25rem;
            --space-2: 0.5rem;
            --space-3: 0.75rem;
            --space-4: 1rem;
            --space-5: 1.25rem;
            --space-6: 1.5rem;
            --space-8: 2rem;
            --space-10: 2.5rem;
            --space-12: 3rem;
            --space-16: 4rem;
            --space-20: 5rem;
            --space-24: 6rem;

            /* Border Radius */
            --radius-sm: 0.375rem;
            --radius-md: 0.5rem;
            --radius-lg: 0.75rem;
            --radius-xl: 1rem;
            --radius-2xl: 1.5rem;
            --radius-3xl: 2rem;

            /* Shadows */
            --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
            --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);
            --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1);
            --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: var(--font-primary);
            background-color: var(--color-dark);
            color: var(--color-light);
            line-height: 1.6;
            overflow-x: hidden;
        }

        /* Navigation */
        .navbar {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            background: rgba(15, 23, 42, 0.95);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid var(--color-gray-800);
            padding: var(--space-4) 0;
        }

        .nav-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 var(--space-6);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            font-size: 1.75rem;
            font-weight: 700;
            color: var(--color-primary);
            text-decoration: none;
            letter-spacing: -0.025em;
        }

        .nav-links {
            display: flex;
            list-style: none;
            gap: var(--space-8);
            align-items: center;
        }

        .nav-links a {
            color: var(--color-gray-300);
            text-decoration: none;
            font-weight: 500;
            transition: color 0.2s ease;
            font-size: 0.95rem;
        }

        .nav-links a:hover {
            color: var(--color-primary);
        }

        .nav-cta {
            display: flex;
            gap: var(--space-3);
            align-items: center;
        }

        .btn {
            padding: var(--space-3) var(--space-6);
            border-radius: var(--radius-lg);
            text-decoration: none;
            font-weight: 600;
            font-size: 0.9rem;
            transition: all 0.2s ease;
            border: none;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            gap: var(--space-2);
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--color-primary), #059669);
            color: white;
            box-shadow: var(--shadow-md);
        }

        .btn-primary:hover {
            transform: translateY(-1px);
            box-shadow: var(--shadow-lg);
        }

        .btn-secondary {
            background: transparent;
            color: var(--color-gray-300);
            border: 1px solid var(--color-gray-700);
        }

        .btn-secondary:hover {
            background: var(--color-gray-800);
            color: var(--color-light);
        }

        /* Mobile Menu */
        .mobile-menu-btn {
            display: none;
            background: none;
            border: none;
            color: var(--color-light);
            font-size: 1.5rem;
            cursor: pointer;
        }

        @media (max-width: 768px) {
            .nav-links {
                display: none;
            }

            .mobile-menu-btn {
                display: block;
            }

            .nav-cta {
                gap: var(--space-2);
            }

            .btn {
                padding: var(--space-2) var(--space-4);
                font-size: 0.85rem;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <a href="#" class="logo">huval</a>

            <ul class="nav-links">
                <li><a href="leaderboard.html">Leaderboard</a></li>
                <li><a href="topics.html">Topics</a></li>
                <li><a href="#categories">Categories</a></li>
                <li><a href="#community">Community</a></li>
                <li><a href="about.html">About</a></li>
            </ul>

            <div class="nav-cta">
                <a href="login.html" class="btn btn-secondary">Sign In</a>
                <a href="register.html" class="btn btn-primary">
                    <i class="fas fa-rocket"></i>
                    Join Now
                </a>
            </div>

            <button class="mobile-menu-btn">
                <i class="fas fa-bars"></i>
            </button>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero">
        <div class="floating-elements">
            <div class="floating-element"></div>
            <div class="floating-element"></div>
            <div class="floating-element"></div>
        </div>
        <div class="hero-container">
            <div class="hero-content">
                <div class="hero-badge">
                    <i class="fas fa-star"></i>
                    <span>Community-Driven AI Evaluation</span>
                </div>

                <h1 class="hero-title">
                    Revolutionizing AI Evaluation
                    <span class="gradient-text">Through Real Community</span>
                </h1>

                <p class="hero-description">
                    Break free from biased datasets. Real users. Real questions. Real results.
                </p>

                <div class="hero-stats">
                    <div class="stat-item">
                        <div class="stat-number">12,847</div>
                        <div class="stat-label">Topics Evaluated</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">156,392</div>
                        <div class="stat-label">AI Responses</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">8,234</div>
                        <div class="stat-label">Active Evaluators</div>
                    </div>
                </div>

                <div class="hero-cta">
                    <a href="create-topic.html" class="btn btn-primary btn-large">
                        <i class="fas fa-plus-circle"></i>
                        Start Evaluating
                    </a>
                    <a href="leaderboard.html" class="btn btn-secondary btn-large">
                        <i class="fas fa-trophy"></i>
                        View Leaderboard
                    </a>
                </div>
            </div>

            <div class="hero-visual">
                <div class="ai-comparison-card">
                    <div class="card-header">
                        <h3>Live AI Comparison</h3>
                        <span class="live-indicator">
                            <i class="fas fa-circle"></i>
                            Live
                        </span>
                    </div>

                    <div class="comparison-item">
                        <div class="ai-response">
                            <div class="ai-name">GPT-4</div>
                            <div class="response-preview">"The solution involves implementing a recursive algorithm..."</div>
                            <div class="vote-count">
                                <i class="fas fa-thumbs-up"></i>
                                847 votes
                            </div>
                        </div>
                    </div>

                    <div class="comparison-item">
                        <div class="ai-response">
                            <div class="ai-name">Claude-3</div>
                            <div class="response-preview">"I'd approach this by breaking down the problem into..."</div>
                            <div class="vote-count">
                                <i class="fas fa-thumbs-up"></i>
                                923 votes
                            </div>
                        </div>
                    </div>

                    <div class="comparison-item winner">
                        <div class="ai-response">
                            <div class="ai-name">Gemini Pro</div>
                            <div class="response-preview">"Here's a comprehensive solution with examples..."</div>
                            <div class="vote-count">
                                <i class="fas fa-crown"></i>
                                1,247 votes
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <style>
        /* Hero Section Styles */
        .hero {
            min-height: 120vh;
            display: flex;
            align-items: center;
            background: linear-gradient(135deg, var(--color-dark) 0%, var(--color-dark-lighter) 100%);
            position: relative;
            overflow: hidden;
            padding: var(--space-24) 0;
            margin-top: 80px;
        }

        .hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at 20% 80%, rgba(16, 185, 129, 0.15) 0%, transparent 60%),
                        radial-gradient(circle at 80% 20%, rgba(99, 102, 241, 0.15) 0%, transparent 60%),
                        radial-gradient(circle at 50% 50%, rgba(245, 158, 11, 0.08) 0%, transparent 70%);
            pointer-events: none;
            animation: gradientShift 8s ease-in-out infinite;
        }

        @keyframes gradientShift {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.8; }
        }

        .hero-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 var(--space-8);
            display: grid;
            grid-template-columns: 1.2fr 0.8fr;
            gap: var(--space-24);
            align-items: center;
            position: relative;
            z-index: 1;
        }

        .hero-badge {
            display: inline-flex;
            align-items: center;
            gap: var(--space-3);
            background: rgba(16, 185, 129, 0.12);
            border: 1px solid rgba(16, 185, 129, 0.25);
            color: var(--color-primary);
            padding: var(--space-4) var(--space-6);
            border-radius: var(--radius-3xl);
            font-size: 1rem;
            font-weight: 600;
            margin-bottom: var(--space-10);
            backdrop-filter: blur(10px);
            animation: badgePulse 3s ease-in-out infinite;
            transition: all 0.3s ease;
        }

        .hero-badge:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(16, 185, 129, 0.2);
        }

        @keyframes badgePulse {
            0%, 100% { box-shadow: 0 0 0 0 rgba(16, 185, 129, 0.4); }
            50% { box-shadow: 0 0 0 10px rgba(16, 185, 129, 0); }
        }

        .hero-title {
            font-size: 4.5rem;
            font-weight: 800;
            line-height: 1.05;
            margin-bottom: var(--space-12);
            letter-spacing: -0.04em;
            animation: titleSlideUp 1s ease-out 0.3s both;
        }

        @keyframes titleSlideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .gradient-text {
            background: linear-gradient(135deg, var(--color-primary), var(--color-secondary), var(--color-accent));
            background-size: 200% 200%;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: gradientFlow 4s ease-in-out infinite;
        }

        @keyframes gradientFlow {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        .hero-description {
            font-size: 1.4rem;
            color: var(--color-gray-300);
            margin-bottom: var(--space-16);
            line-height: 1.7;
            animation: descriptionFadeIn 1s ease-out 0.6s both;
            max-width: 90%;
        }

        @keyframes descriptionFadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .hero-stats {
            display: flex;
            gap: var(--space-12);
            margin-bottom: var(--space-20);
            animation: statsSlideUp 1s ease-out 0.9s both;
        }

        .stat-item {
            text-align: left;
            position: relative;
            padding: var(--space-6);
            border-radius: var(--radius-xl);
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .stat-item:hover {
            transform: translateY(-5px);
            background: rgba(255, 255, 255, 0.08);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 800;
            color: var(--color-primary);
            margin-bottom: var(--space-2);
            display: block;
            animation: numberCount 2s ease-out 1.2s both;
        }

        .stat-label {
            font-size: 1rem;
            color: var(--color-gray-300);
            font-weight: 600;
            letter-spacing: 0.02em;
        }

        @keyframes statsSlideUp {
            from {
                opacity: 0;
                transform: translateY(40px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes numberCount {
            from {
                opacity: 0;
                transform: scale(0.5);
            }
            to {
                opacity: 1;
                transform: scale(1);
            }
        }

        .hero-cta {
            display: flex;
            gap: var(--space-6);
            flex-wrap: wrap;
            animation: ctaFadeIn 1s ease-out 1.2s both;
        }

        .btn-large {
            padding: var(--space-5) var(--space-10);
            font-size: 1.2rem;
            font-weight: 700;
            border-radius: var(--radius-2xl);
            position: relative;
            overflow: hidden;
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        }

        .btn-large::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.6s ease;
        }

        .btn-large:hover::before {
            left: 100%;
        }

        .btn-large:hover {
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
        }

        @keyframes ctaFadeIn {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* AI Comparison Card */
        .hero-visual {
            display: flex;
            justify-content: center;
            align-items: center;
            animation: visualSlideIn 1s ease-out 0.6s both;
        }

        .ai-comparison-card {
            background: var(--color-light);
            border-radius: var(--radius-3xl);
            padding: var(--space-10);
            box-shadow: 0 25px 60px rgba(0, 0, 0, 0.3);
            width: 100%;
            max-width: 480px;
            color: var(--color-gray-900);
            position: relative;
            overflow: hidden;
            transition: all 0.4s ease;
        }

        .ai-comparison-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--color-primary), var(--color-secondary), var(--color-accent));
            background-size: 200% 100%;
            animation: gradientSlide 3s ease-in-out infinite;
        }

        .ai-comparison-card:hover {
            transform: translateY(-10px) scale(1.02);
            box-shadow: 0 35px 80px rgba(0, 0, 0, 0.4);
        }

        @keyframes visualSlideIn {
            from {
                opacity: 0;
                transform: translateX(50px) scale(0.9);
            }
            to {
                opacity: 1;
                transform: translateX(0) scale(1);
            }
        }

        @keyframes gradientSlide {
            0%, 100% { background-position: 0% 0%; }
            50% { background-position: 200% 0%; }
        }

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--space-6);
            padding-bottom: var(--space-4);
            border-bottom: 1px solid var(--color-gray-200);
        }

        .card-header h3 {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--color-gray-900);
        }

        .live-indicator {
            display: flex;
            align-items: center;
            gap: var(--space-2);
            color: var(--color-primary);
            font-size: 0.875rem;
            font-weight: 500;
        }

        .live-indicator i {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .comparison-item {
            margin-bottom: var(--space-4);
            padding: var(--space-4);
            border-radius: var(--radius-lg);
            background: var(--color-gray-50);
            transition: all 0.2s ease;
        }

        .comparison-item:hover {
            background: var(--color-gray-100);
            transform: translateY(-1px);
        }

        .comparison-item.winner {
            background: linear-gradient(135deg, rgba(16, 185, 129, 0.1), rgba(16, 185, 129, 0.05));
            border: 1px solid rgba(16, 185, 129, 0.2);
        }

        .ai-name {
            font-weight: 600;
            color: var(--color-gray-900);
            margin-bottom: var(--space-2);
        }

        .response-preview {
            font-size: 0.875rem;
            color: var(--color-gray-600);
            margin-bottom: var(--space-3);
            line-height: 1.4;
        }

        .vote-count {
            display: flex;
            align-items: center;
            gap: var(--space-2);
            font-size: 0.875rem;
            font-weight: 500;
            color: var(--color-primary);
        }

        .comparison-item.winner .vote-count {
            color: var(--color-accent);
        }

        /* Floating Elements */
        .floating-elements {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            pointer-events: none;
            overflow: hidden;
        }

        .floating-element {
            position: absolute;
            background: linear-gradient(135deg, rgba(16, 185, 129, 0.1), rgba(99, 102, 241, 0.1));
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }

        .floating-element:nth-child(1) {
            width: 60px;
            height: 60px;
            top: 20%;
            left: 10%;
            animation-delay: 0s;
        }

        .floating-element:nth-child(2) {
            width: 40px;
            height: 40px;
            top: 60%;
            right: 15%;
            animation-delay: 2s;
        }

        .floating-element:nth-child(3) {
            width: 80px;
            height: 80px;
            bottom: 30%;
            left: 20%;
            animation-delay: 4s;
        }

        @keyframes float {
            0%, 100% {
                transform: translateY(0px) rotate(0deg);
                opacity: 0.3;
            }
            50% {
                transform: translateY(-20px) rotate(180deg);
                opacity: 0.6;
            }
        }

        /* Enhanced Spacing */
        .section-spacing {
            padding: var(--space-24) 0;
        }

        .section-spacing-large {
            padding: var(--space-24) 0 var(--space-24) 0;
        }

        @media (max-width: 768px) {
            .hero-container {
                grid-template-columns: 1fr;
                gap: var(--space-16);
                text-align: center;
            }

            .hero-title {
                font-size: 3rem;
            }

            .hero-description {
                font-size: 1.2rem;
            }

            .hero-stats {
                justify-content: center;
                gap: var(--space-8);
                grid-template-columns: repeat(2, 1fr);
                display: grid;
            }

            .hero-cta {
                justify-content: center;
                flex-direction: column;
                align-items: center;
            }

            .section-title {
                font-size: 3rem;
            }
        }
    </style>

    <!-- How It Works Section -->
    <section class="how-it-works">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">How Huval Works</h2>
                <p class="section-description">
                    Real users. Real questions. Unbiased results.
                </p>
            </div>

            <div class="steps-grid">
                <div class="step-card">
                    <div class="step-number">01</div>
                    <div class="step-icon">
                        <i class="fas fa-lightbulb"></i>
                    </div>
                    <h3 class="step-title">Create Topics</h3>
                    <p class="step-description">
                        Submit real questions across coding, knowledge, and creative categories.
                    </p>
                    <div class="step-visual">
                        <div class="mini-form">
                            <div class="form-field">
                                <div class="field-label">Category</div>
                                <div class="field-value">Coding</div>
                            </div>
                            <div class="form-field">
                                <div class="field-label">Question</div>
                                <div class="field-value">How to implement...</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="step-card">
                    <div class="step-number">02</div>
                    <div class="step-icon">
                        <i class="fas fa-robot"></i>
                    </div>
                    <h3 class="step-title">AI Responses</h3>
                    <p class="step-description">
                        Multiple AI systems respond anonymously. No bias. Pure performance.
                    </p>
                    <div class="step-visual">
                        <div class="ai-responses-preview">
                            <div class="response-item">
                                <div class="response-avatar">AI</div>
                                <div class="response-text">Response A...</div>
                            </div>
                            <div class="response-item">
                                <div class="response-avatar">AI</div>
                                <div class="response-text">Response B...</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="step-card">
                    <div class="step-number">03</div>
                    <div class="step-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <h3 class="step-title">Community Evaluation</h3>
                    <p class="step-description">
                        Community votes on quality and accuracy. Fair. Transparent. Unbiased.
                    </p>
                    <div class="step-visual">
                        <div class="voting-interface">
                            <div class="vote-buttons">
                                <button class="vote-btn upvote">
                                    <i class="fas fa-thumbs-up"></i>
                                    <span>847</span>
                                </button>
                                <button class="vote-btn downvote">
                                    <i class="fas fa-thumbs-down"></i>
                                    <span>23</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="step-card">
                    <div class="step-number">04</div>
                    <div class="step-icon">
                        <i class="fas fa-trophy"></i>
                    </div>
                    <h3 class="step-title">Global Rankings</h3>
                    <p class="step-description">
                        Real-world rankings based on actual performance. See who truly leads.
                    </p>
                    <div class="step-visual">
                        <div class="leaderboard-preview">
                            <div class="rank-item">
                                <span class="rank">#1</span>
                                <span class="ai-name">Claude-3</span>
                                <span class="score">94.2%</span>
                            </div>
                            <div class="rank-item">
                                <span class="rank">#2</span>
                                <span class="ai-name">GPT-4</span>
                                <span class="score">92.8%</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <style>
        /* How It Works Section */
        .how-it-works {
            padding: var(--space-24) 0 var(--space-24) 0;
            background: var(--color-dark-lighter);
            position: relative;
            overflow: hidden;
        }

        .how-it-works::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at 30% 20%, rgba(16, 185, 129, 0.05) 0%, transparent 50%),
                        radial-gradient(circle at 70% 80%, rgba(99, 102, 241, 0.05) 0%, transparent 50%);
            pointer-events: none;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 var(--space-8);
            position: relative;
            z-index: 1;
        }

        .section-header {
            text-align: center;
            margin-bottom: var(--space-24);
            animation: sectionHeaderFadeIn 1s ease-out;
        }

        .section-title {
            font-size: 4rem;
            font-weight: 800;
            margin-bottom: var(--space-8);
            letter-spacing: -0.04em;
            background: linear-gradient(135deg, var(--color-light), var(--color-gray-300));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .section-description {
            font-size: 1.4rem;
            color: var(--color-gray-300);
            max-width: 700px;
            margin: 0 auto;
            line-height: 1.7;
            opacity: 0.9;
        }

        @keyframes sectionHeaderFadeIn {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .steps-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: var(--space-12);
        }

        .step-card {
            background: var(--color-light);
            border-radius: var(--radius-3xl);
            padding: var(--space-12);
            color: var(--color-gray-900);
            position: relative;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            opacity: 0;
            transform: translateY(50px);
            animation: stepCardSlideUp 0.8s ease-out forwards;
            overflow: hidden;
        }

        .step-card:nth-child(1) { animation-delay: 0.1s; }
        .step-card:nth-child(2) { animation-delay: 0.2s; }
        .step-card:nth-child(3) { animation-delay: 0.3s; }
        .step-card:nth-child(4) { animation-delay: 0.4s; }

        .step-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 6px;
            background: linear-gradient(90deg, var(--color-primary), var(--color-secondary));
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .step-card:hover {
            transform: translateY(-12px) scale(1.02);
            box-shadow: 0 30px 60px rgba(0, 0, 0, 0.2);
        }

        .step-card:hover::before {
            opacity: 1;
        }

        @keyframes stepCardSlideUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .step-number {
            position: absolute;
            top: var(--space-8);
            right: var(--space-8);
            background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
            color: white;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 800;
            font-size: 1.1rem;
            box-shadow: 0 8px 20px rgba(16, 185, 129, 0.3);
            animation: numberPulse 2s ease-in-out infinite;
        }

        .step-icon {
            background: linear-gradient(135deg, rgba(16, 185, 129, 0.15), rgba(99, 102, 241, 0.15));
            color: var(--color-primary);
            width: 80px;
            height: 80px;
            border-radius: var(--radius-2xl);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            margin-bottom: var(--space-8);
            transition: all 0.3s ease;
            border: 2px solid rgba(16, 185, 129, 0.2);
        }

        .step-card:hover .step-icon {
            transform: scale(1.1) rotate(5deg);
            background: linear-gradient(135deg, rgba(16, 185, 129, 0.25), rgba(99, 102, 241, 0.25));
            box-shadow: 0 10px 25px rgba(16, 185, 129, 0.2);
        }

        @keyframes numberPulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        .step-title {
            font-size: 1.75rem;
            font-weight: 700;
            margin-bottom: var(--space-6);
            color: var(--color-gray-900);
            letter-spacing: -0.02em;
        }

        .step-description {
            color: var(--color-gray-600);
            line-height: 1.7;
            margin-bottom: var(--space-8);
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .step-visual {
            background: linear-gradient(135deg, var(--color-gray-50), var(--color-gray-100));
            border-radius: var(--radius-2xl);
            padding: var(--space-6);
            border: 1px solid var(--color-gray-200);
            transition: all 0.3s ease;
        }

        .step-card:hover .step-visual {
            background: linear-gradient(135deg, var(--color-gray-100), var(--color-gray-50));
            transform: scale(1.02);
            box-shadow: inset 0 2px 10px rgba(0, 0, 0, 0.05);
        }

        /* Step Visual Components */
        .mini-form {
            display: flex;
            flex-direction: column;
            gap: var(--space-3);
        }

        .form-field {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .field-label {
            font-size: 0.875rem;
            color: var(--color-gray-500);
            font-weight: 500;
        }

        .field-value {
            font-size: 0.875rem;
            color: var(--color-gray-900);
            font-weight: 500;
        }

        .ai-responses-preview {
            display: flex;
            flex-direction: column;
            gap: var(--space-2);
        }

        .response-item {
            display: flex;
            align-items: center;
            gap: var(--space-3);
        }

        .response-avatar {
            width: 24px;
            height: 24px;
            background: var(--color-primary);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.75rem;
            font-weight: 600;
        }

        .response-text {
            font-size: 0.875rem;
            color: var(--color-gray-600);
        }

        .voting-interface {
            display: flex;
            justify-content: center;
        }

        .vote-buttons {
            display: flex;
            gap: var(--space-4);
        }

        .vote-btn {
            display: flex;
            align-items: center;
            gap: var(--space-2);
            padding: var(--space-2) var(--space-4);
            border: none;
            border-radius: var(--radius-md);
            font-size: 0.875rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .vote-btn.upvote {
            background: rgba(16, 185, 129, 0.1);
            color: var(--color-primary);
        }

        .vote-btn.downvote {
            background: rgba(239, 68, 68, 0.1);
            color: #EF4444;
        }

        .leaderboard-preview {
            display: flex;
            flex-direction: column;
            gap: var(--space-2);
        }

        .rank-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: var(--space-2) var(--space-3);
            background: white;
            border-radius: var(--radius-md);
            font-size: 0.875rem;
        }

        .rank {
            font-weight: 700;
            color: var(--color-primary);
        }

        .ai-name {
            font-weight: 500;
            color: var(--color-gray-900);
        }

        .score {
            font-weight: 600;
            color: var(--color-gray-700);
        }

        @media (max-width: 768px) {
            .section-title {
                font-size: 2.5rem;
            }

            .steps-grid {
                grid-template-columns: 1fr;
                gap: var(--space-6);
            }
        }
    </style>

    <!-- Leaderboard Preview Section -->
    <section class="leaderboard-preview">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">Live AI Leaderboard</h2>
                <p class="section-description">
                    See which AI models are performing best across different categories,
                    ranked by real user evaluations and community feedback.
                </p>
            </div>

            <div class="leaderboard-container">
                <div class="leaderboard-filters">
                    <button class="filter-btn active">All Categories</button>
                    <button class="filter-btn">Coding</button>
                    <button class="filter-btn">General Knowledge</button>
                    <button class="filter-btn">Creative Writing</button>
                </div>

                <div class="leaderboard-table">
                    <div class="table-header">
                        <div class="rank-col">Rank</div>
                        <div class="ai-col">AI Model</div>
                        <div class="score-col">Score</div>
                        <div class="votes-col">Total Votes</div>
                        <div class="trend-col">Trend</div>
                    </div>

                    <div class="table-row winner">
                        <div class="rank-col">
                            <i class="fas fa-crown"></i>
                            <span>1</span>
                        </div>
                        <div class="ai-col">
                            <div class="ai-info">
                                <div class="ai-name">Claude-3 Opus</div>
                                <div class="ai-provider">Anthropic</div>
                            </div>
                        </div>
                        <div class="score-col">
                            <div class="score-bar">
                                <div class="score-fill" style="width: 94%"></div>
                            </div>
                            <span class="score-text">94.2%</span>
                        </div>
                        <div class="votes-col">23,847</div>
                        <div class="trend-col">
                            <i class="fas fa-arrow-up trend-up"></i>
                            <span>+2.3%</span>
                        </div>
                    </div>

                    <div class="table-row">
                        <div class="rank-col">2</div>
                        <div class="ai-col">
                            <div class="ai-info">
                                <div class="ai-name">GPT-4 Turbo</div>
                                <div class="ai-provider">OpenAI</div>
                            </div>
                        </div>
                        <div class="score-col">
                            <div class="score-bar">
                                <div class="score-fill" style="width: 92%"></div>
                            </div>
                            <span class="score-text">92.8%</span>
                        </div>
                        <div class="votes-col">21,456</div>
                        <div class="trend-col">
                            <i class="fas fa-arrow-down trend-down"></i>
                            <span>-0.8%</span>
                        </div>
                    </div>

                    <div class="table-row">
                        <div class="rank-col">3</div>
                        <div class="ai-col">
                            <div class="ai-info">
                                <div class="ai-name">Gemini Pro</div>
                                <div class="ai-provider">Google</div>
                            </div>
                        </div>
                        <div class="score-col">
                            <div class="score-bar">
                                <div class="score-fill" style="width: 89%"></div>
                            </div>
                            <span class="score-text">89.4%</span>
                        </div>
                        <div class="votes-col">18,923</div>
                        <div class="trend-col">
                            <i class="fas fa-arrow-up trend-up"></i>
                            <span>+1.2%</span>
                        </div>
                    </div>
                </div>

                <div class="leaderboard-cta">
                    <a href="leaderboard.html" class="btn btn-primary">
                        <i class="fas fa-chart-line"></i>
                        View Full Leaderboard
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Categories Showcase Section -->
    <section class="categories-showcase">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">Evaluation Categories</h2>
                <p class="section-description">
                    Comprehensive AI evaluation across diverse domains to understand
                    model strengths and weaknesses in different contexts.
                </p>
            </div>

            <div class="categories-grid">
                <div class="category-card">
                    <div class="category-icon">
                        <i class="fas fa-code"></i>
                    </div>
                    <h3 class="category-title">Coding & Programming</h3>
                    <p class="category-description">
                        Algorithm implementation, debugging, code optimization,
                        and software architecture challenges.
                    </p>
                    <div class="category-stats">
                        <div class="stat">
                            <span class="stat-number">4,234</span>
                            <span class="stat-label">Topics</span>
                        </div>
                        <div class="stat">
                            <span class="stat-number">67,892</span>
                            <span class="stat-label">Responses</span>
                        </div>
                    </div>
                    <div class="category-languages">
                        <span class="language-tag">Python</span>
                        <span class="language-tag">JavaScript</span>
                        <span class="language-tag">Java</span>
                        <span class="language-tag">+12 more</span>
                    </div>
                </div>

                <div class="category-card">
                    <div class="category-icon">
                        <i class="fas fa-brain"></i>
                    </div>
                    <h3 class="category-title">General Knowledge</h3>
                    <p class="category-description">
                        Science, history, current events, and factual questions
                        testing AI knowledge and reasoning capabilities.
                    </p>
                    <div class="category-stats">
                        <div class="stat">
                            <span class="stat-number">3,567</span>
                            <span class="stat-label">Topics</span>
                        </div>
                        <div class="stat">
                            <span class="stat-number">52,341</span>
                            <span class="stat-label">Responses</span>
                        </div>
                    </div>
                    <div class="category-languages">
                        <span class="language-tag">Science</span>
                        <span class="language-tag">History</span>
                        <span class="language-tag">Geography</span>
                        <span class="language-tag">+8 more</span>
                    </div>
                </div>

                <div class="category-card">
                    <div class="category-icon">
                        <i class="fas fa-pen-fancy"></i>
                    </div>
                    <h3 class="category-title">Creative Writing</h3>
                    <p class="category-description">
                        Storytelling, poetry, creative content generation,
                        and artistic expression evaluation.
                    </p>
                    <div class="category-stats">
                        <div class="stat">
                            <span class="stat-number">2,891</span>
                            <span class="stat-label">Topics</span>
                        </div>
                        <div class="stat">
                            <span class="stat-number">41,267</span>
                            <span class="stat-label">Responses</span>
                        </div>
                    </div>
                    <div class="category-languages">
                        <span class="language-tag">Fiction</span>
                        <span class="language-tag">Poetry</span>
                        <span class="language-tag">Scripts</span>
                        <span class="language-tag">+5 more</span>
                    </div>
                </div>

                <div class="category-card">
                    <div class="category-icon">
                        <i class="fas fa-question-circle"></i>
                    </div>
                    <h3 class="category-title">Q&A Support</h3>
                    <p class="category-description">
                        Help desk scenarios, customer support, and problem-solving
                        in real-world assistance contexts.
                    </p>
                    <div class="category-stats">
                        <div class="stat">
                            <span class="stat-number">2,156</span>
                            <span class="stat-label">Topics</span>
                        </div>
                        <div class="stat">
                            <span class="stat-number">34,892</span>
                            <span class="stat-label">Responses</span>
                        </div>
                    </div>
                    <div class="category-languages">
                        <span class="language-tag">Support</span>
                        <span class="language-tag">Troubleshooting</span>
                        <span class="language-tag">Guidance</span>
                        <span class="language-tag">+3 more</span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <style>
        /* Leaderboard Preview Section */
        .leaderboard-preview {
            padding: var(--space-24) 0;
            background: var(--color-dark);
        }

        .leaderboard-container {
            background: var(--color-light);
            border-radius: var(--radius-2xl);
            padding: var(--space-8);
            color: var(--color-gray-900);
            box-shadow: var(--shadow-2xl);
        }

        .leaderboard-filters {
            display: flex;
            gap: var(--space-3);
            margin-bottom: var(--space-8);
            flex-wrap: wrap;
        }

        .filter-btn {
            padding: var(--space-3) var(--space-6);
            border: 1px solid var(--color-gray-300);
            background: white;
            color: var(--color-gray-600);
            border-radius: var(--radius-lg);
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .filter-btn.active,
        .filter-btn:hover {
            background: var(--color-primary);
            color: white;
            border-color: var(--color-primary);
        }

        .leaderboard-table {
            background: var(--color-gray-50);
            border-radius: var(--radius-xl);
            overflow: hidden;
        }

        .table-header {
            display: grid;
            grid-template-columns: 80px 1fr 120px 100px 100px;
            gap: var(--space-4);
            padding: var(--space-4) var(--space-6);
            background: var(--color-gray-100);
            font-weight: 600;
            font-size: 0.875rem;
            color: var(--color-gray-700);
        }

        .table-row {
            display: grid;
            grid-template-columns: 80px 1fr 120px 100px 100px;
            gap: var(--space-4);
            padding: var(--space-4) var(--space-6);
            border-bottom: 1px solid var(--color-gray-200);
            align-items: center;
            transition: background 0.2s ease;
        }

        .table-row:hover {
            background: white;
        }

        .table-row.winner {
            background: linear-gradient(135deg, rgba(245, 158, 11, 0.1), rgba(245, 158, 11, 0.05));
        }

        .rank-col {
            display: flex;
            align-items: center;
            gap: var(--space-2);
            font-weight: 600;
        }

        .rank-col i {
            color: var(--color-accent);
        }

        .ai-info {
            display: flex;
            flex-direction: column;
        }

        .ai-name {
            font-weight: 600;
            color: var(--color-gray-900);
        }

        .ai-provider {
            font-size: 0.875rem;
            color: var(--color-gray-500);
        }

        .score-col {
            display: flex;
            align-items: center;
            gap: var(--space-3);
        }

        .score-bar {
            width: 60px;
            height: 8px;
            background: var(--color-gray-200);
            border-radius: var(--radius-sm);
            overflow: hidden;
        }

        .score-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--color-primary), #059669);
            border-radius: var(--radius-sm);
        }

        .score-text {
            font-weight: 600;
            font-size: 0.875rem;
        }

        .votes-col {
            font-weight: 500;
            color: var(--color-gray-600);
        }

        .trend-col {
            display: flex;
            align-items: center;
            gap: var(--space-1);
            font-size: 0.875rem;
            font-weight: 500;
        }

        .trend-up {
            color: var(--color-primary);
        }

        .trend-down {
            color: #EF4444;
        }

        .leaderboard-cta {
            text-align: center;
            margin-top: var(--space-8);
        }

        /* Categories Showcase Section */
        .categories-showcase {
            padding: var(--space-24) 0;
            background: var(--color-dark-lighter);
        }

        .categories-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: var(--space-8);
        }

        .category-card {
            background: var(--color-light);
            border-radius: var(--radius-2xl);
            padding: var(--space-8);
            color: var(--color-gray-900);
            box-shadow: var(--shadow-lg);
            transition: transform 0.2s ease;
        }

        .category-card:hover {
            transform: translateY(-4px);
        }

        .category-icon {
            background: linear-gradient(135deg, var(--color-primary), #059669);
            color: white;
            width: 60px;
            height: 60px;
            border-radius: var(--radius-xl);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            margin-bottom: var(--space-6);
        }

        .category-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: var(--space-4);
            color: var(--color-gray-900);
        }

        .category-description {
            color: var(--color-gray-600);
            line-height: 1.6;
            margin-bottom: var(--space-6);
        }

        .category-stats {
            display: flex;
            gap: var(--space-6);
            margin-bottom: var(--space-6);
            padding: var(--space-4);
            background: var(--color-gray-50);
            border-radius: var(--radius-lg);
        }

        .stat {
            text-align: center;
        }

        .stat-number {
            display: block;
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--color-primary);
        }

        .stat-label {
            font-size: 0.875rem;
            color: var(--color-gray-500);
        }

        .category-languages {
            display: flex;
            flex-wrap: wrap;
            gap: var(--space-2);
        }

        .language-tag {
            background: rgba(16, 185, 129, 0.1);
            color: var(--color-primary);
            padding: var(--space-1) var(--space-3);
            border-radius: var(--radius-md);
            font-size: 0.875rem;
            font-weight: 500;
        }

        @media (max-width: 768px) {
            .table-header,
            .table-row {
                grid-template-columns: 60px 1fr 80px;
                gap: var(--space-2);
            }

            .votes-col,
            .trend-col {
                display: none;
            }

            .leaderboard-filters {
                justify-content: center;
            }

            .categories-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>

    <!-- Community Impact Section -->
    <section class="community-impact">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">Community Impact</h2>
                <p class="section-description">
                    Join thousands of evaluators shaping the future of AI through
                    transparent, unbiased assessment and real-world testing.
                </p>
            </div>

            <div class="impact-grid">
                <div class="impact-stats">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-number">8,234</div>
                            <div class="stat-label">Active Evaluators</div>
                            <div class="stat-growth">+23% this month</div>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-comments"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-number">156,392</div>
                            <div class="stat-label">AI Responses Evaluated</div>
                            <div class="stat-growth">+45% this month</div>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-lightbulb"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-number">12,847</div>
                            <div class="stat-label">Topics Created</div>
                            <div class="stat-growth">+18% this month</div>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-robot"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-number">47</div>
                            <div class="stat-label">AI Models Evaluated</div>
                            <div class="stat-growth">+12% this month</div>
                        </div>
                    </div>
                </div>

                <div class="testimonials">
                    <div class="testimonial-card">
                        <div class="testimonial-content">
                            "Huval has completely changed how I evaluate AI models. Instead of relying on
                            marketing claims, I can see real performance data from actual users."
                        </div>
                        <div class="testimonial-author">
                            <div class="author-avatar">
                                <i class="fas fa-user"></i>
                            </div>
                            <div class="author-info">
                                <div class="author-name">Sarah Chen</div>
                                <div class="author-role">ML Engineer</div>
                            </div>
                        </div>
                    </div>

                    <div class="testimonial-card">
                        <div class="testimonial-content">
                            "The community-driven approach gives me confidence in the rankings.
                            It's refreshing to see unbiased AI evaluation."
                        </div>
                        <div class="testimonial-author">
                            <div class="author-avatar">
                                <i class="fas fa-user"></i>
                            </div>
                            <div class="author-info">
                                <div class="author-name">Marcus Rodriguez</div>
                                <div class="author-role">Software Developer</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- For AI Providers Section -->
    <section class="ai-providers">
        <div class="container">
            <div class="provider-content">
                <div class="provider-info">
                    <div class="provider-badge">
                        <i class="fas fa-robot"></i>
                        <span>For AI Providers</span>
                    </div>

                    <h2 class="provider-title">
                        Get Real Performance Insights
                        <span class="gradient-text">For Your AI Models</span>
                    </h2>

                    <p class="provider-description">
                        Connect your AI models to Huval and receive comprehensive performance analytics
                        based on real user evaluations. Understand your model's strengths and areas for improvement.
                    </p>

                    <div class="provider-benefits">
                        <div class="benefit-item">
                            <i class="fas fa-chart-line"></i>
                            <div class="benefit-content">
                                <h4>Performance Analytics</h4>
                                <p>Detailed insights into your model's performance across different categories and use cases.</p>
                            </div>
                        </div>

                        <div class="benefit-item">
                            <i class="fas fa-users"></i>
                            <div class="benefit-content">
                                <h4>Real User Feedback</h4>
                                <p>Understand how real users perceive and interact with your AI model's responses.</p>
                            </div>
                        </div>

                        <div class="benefit-item">
                            <i class="fas fa-target"></i>
                            <div class="benefit-content">
                                <h4>Improvement Tracking</h4>
                                <p>Monitor your model's progress over time and measure the impact of updates.</p>
                            </div>
                        </div>
                    </div>

                    <div class="provider-cta">
                        <a href="register.html" class="btn btn-primary btn-large">
                            <i class="fas fa-plus"></i>
                            Add Your AI Model
                        </a>
                        <a href="#" class="btn btn-secondary btn-large">
                            <i class="fas fa-info-circle"></i>
                            Learn More
                        </a>
                    </div>
                </div>

                <div class="provider-visual">
                    <div class="analytics-dashboard">
                        <div class="dashboard-header">
                            <h3>Model Performance Dashboard</h3>
                            <span class="model-name">Your AI Model</span>
                        </div>

                        <div class="performance-metrics">
                            <div class="metric">
                                <div class="metric-label">Overall Score</div>
                                <div class="metric-value">87.3%</div>
                                <div class="metric-trend">
                                    <i class="fas fa-arrow-up"></i>
                                    +3.2%
                                </div>
                            </div>

                            <div class="metric">
                                <div class="metric-label">Total Evaluations</div>
                                <div class="metric-value">15,234</div>
                                <div class="metric-trend">
                                    <i class="fas fa-arrow-up"></i>
                                    +12%
                                </div>
                            </div>
                        </div>

                        <div class="category-breakdown">
                            <div class="category-item">
                                <span class="category-name">Coding</span>
                                <div class="category-bar">
                                    <div class="category-fill" style="width: 92%"></div>
                                </div>
                                <span class="category-score">92%</span>
                            </div>

                            <div class="category-item">
                                <span class="category-name">General Knowledge</span>
                                <div class="category-bar">
                                    <div class="category-fill" style="width: 85%"></div>
                                </div>
                                <span class="category-score">85%</span>
                            </div>

                            <div class="category-item">
                                <span class="category-name">Creative Writing</span>
                                <div class="category-bar">
                                    <div class="category-fill" style="width: 78%"></div>
                                </div>
                                <span class="category-score">78%</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Final CTA Section -->
    <section class="final-cta">
        <div class="container">
            <div class="cta-content">
                <h2 class="cta-title">
                    Ready to Shape the Future of AI?
                </h2>
                <p class="cta-description">
                    Join our community of evaluators and help create the most comprehensive,
                    unbiased AI evaluation platform. Your voice matters in determining which AI models truly excel.
                </p>

                <div class="cta-options">
                    <div class="cta-option">
                        <div class="option-icon">
                            <i class="fas fa-user-plus"></i>
                        </div>
                        <h3>Join as Evaluator</h3>
                        <p>Start evaluating AI responses and contribute to the global leaderboard.</p>
                        <a href="register.html" class="btn btn-primary">
                            <i class="fas fa-rocket"></i>
                            Get Started Free
                        </a>
                    </div>

                    <div class="cta-option">
                        <div class="option-icon">
                            <i class="fas fa-robot"></i>
                        </div>
                        <h3>Add Your AI Model</h3>
                        <p>Connect your AI model and receive detailed performance insights.</p>
                        <a href="register.html" class="btn btn-secondary">
                            <i class="fas fa-plus"></i>
                            Add Model
                        </a>
                    </div>
                </div>

                <div class="trust-indicators">
                    <div class="trust-item">
                        <i class="fas fa-shield-alt"></i>
                        <span>Secure & Private</span>
                    </div>
                    <div class="trust-item">
                        <i class="fas fa-users"></i>
                        <span>Community Driven</span>
                    </div>
                    <div class="trust-item">
                        <i class="fas fa-chart-line"></i>
                        <span>Transparent Results</span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-brand">
                    <a href="#" class="footer-logo">huval</a>
                    <p class="footer-description">
                        Revolutionary AI evaluation platform powered by community-driven assessment.
                    </p>
                    <div class="social-links">
                        <a href="#" class="social-link">
                            <i class="fab fa-twitter"></i>
                        </a>
                        <a href="#" class="social-link">
                            <i class="fab fa-github"></i>
                        </a>
                        <a href="#" class="social-link">
                            <i class="fab fa-discord"></i>
                        </a>
                        <a href="#" class="social-link">
                            <i class="fab fa-linkedin"></i>
                        </a>
                    </div>
                </div>

                <div class="footer-links">
                    <div class="link-group">
                        <h4>Platform</h4>
                        <a href="leaderboard.html">Leaderboard</a>
                        <a href="topics.html">Browse Topics</a>
                        <a href="create-topic.html">Create Topic</a>
                        <a href="#categories">Categories</a>
                    </div>

                    <div class="link-group">
                        <h4>Community</h4>
                        <a href="#community">Join Community</a>
                        <a href="#">Guidelines</a>
                        <a href="#">Help Center</a>
                        <a href="#">Contact</a>
                    </div>

                    <div class="link-group">
                        <h4>For Developers</h4>
                        <a href="#">API Documentation</a>
                        <a href="#">Add AI Model</a>
                        <a href="#">Integration Guide</a>
                        <a href="#">Status</a>
                    </div>

                    <div class="link-group">
                        <h4>Legal</h4>
                        <a href="#">Privacy Policy</a>
                        <a href="#">Terms of Service</a>
                        <a href="#">Cookie Policy</a>
                        <a href="#">GDPR</a>
                    </div>
                </div>
            </div>

            <div class="footer-bottom">
                <p>&copy; 2025 Huval. All rights reserved.</p>
                <p>Building the future of AI evaluation through community collaboration.</p>
            </div>
        </div>
    </footer>

    <style>
        /* Community Impact Section */
        .community-impact {
            padding: var(--space-24) 0;
            background: var(--color-dark);
        }

        .impact-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--space-16);
            align-items: start;
        }

        .impact-stats {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: var(--space-6);
        }

        .stat-card {
            background: var(--color-light);
            border-radius: var(--radius-2xl);
            padding: var(--space-6);
            color: var(--color-gray-900);
            box-shadow: var(--shadow-lg);
            display: flex;
            align-items: center;
            gap: var(--space-4);
        }

        .stat-icon {
            background: linear-gradient(135deg, var(--color-primary), #059669);
            color: white;
            width: 50px;
            height: 50px;
            border-radius: var(--radius-xl);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.25rem;
        }

        .stat-content {
            flex: 1;
        }

        .stat-number {
            font-size: 1.75rem;
            font-weight: 700;
            color: var(--color-gray-900);
            margin-bottom: var(--space-1);
        }

        .stat-label {
            font-size: 0.875rem;
            color: var(--color-gray-600);
            margin-bottom: var(--space-1);
        }

        .stat-growth {
            font-size: 0.875rem;
            color: var(--color-primary);
            font-weight: 500;
        }

        .testimonials {
            display: flex;
            flex-direction: column;
            gap: var(--space-6);
        }

        .testimonial-card {
            background: var(--color-light);
            border-radius: var(--radius-2xl);
            padding: var(--space-8);
            color: var(--color-gray-900);
            box-shadow: var(--shadow-lg);
        }

        .testimonial-content {
            font-size: 1.1rem;
            line-height: 1.6;
            color: var(--color-gray-700);
            margin-bottom: var(--space-6);
            font-style: italic;
        }

        .testimonial-author {
            display: flex;
            align-items: center;
            gap: var(--space-4);
        }

        .author-avatar {
            background: var(--color-primary);
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .author-name {
            font-weight: 600;
            color: var(--color-gray-900);
        }

        .author-role {
            font-size: 0.875rem;
            color: var(--color-gray-500);
        }

        /* AI Providers Section */
        .ai-providers {
            padding: var(--space-24) 0;
            background: var(--color-dark-lighter);
        }

        .provider-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--space-16);
            align-items: center;
        }

        .provider-badge {
            display: inline-flex;
            align-items: center;
            gap: var(--space-2);
            background: rgba(99, 102, 241, 0.1);
            border: 1px solid rgba(99, 102, 241, 0.2);
            color: var(--color-secondary);
            padding: var(--space-2) var(--space-4);
            border-radius: var(--radius-2xl);
            font-size: 0.875rem;
            font-weight: 500;
            margin-bottom: var(--space-6);
        }

        .provider-title {
            font-size: 3rem;
            font-weight: 700;
            line-height: 1.1;
            margin-bottom: var(--space-6);
            letter-spacing: -0.025em;
        }

        .provider-description {
            font-size: 1.25rem;
            color: var(--color-gray-300);
            margin-bottom: var(--space-8);
            line-height: 1.6;
        }

        .provider-benefits {
            display: flex;
            flex-direction: column;
            gap: var(--space-6);
            margin-bottom: var(--space-10);
        }

        .benefit-item {
            display: flex;
            align-items: flex-start;
            gap: var(--space-4);
        }

        .benefit-item i {
            background: rgba(16, 185, 129, 0.1);
            color: var(--color-primary);
            width: 40px;
            height: 40px;
            border-radius: var(--radius-lg);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.1rem;
            margin-top: var(--space-1);
        }

        .benefit-content h4 {
            font-size: 1.1rem;
            font-weight: 600;
            color: var(--color-light);
            margin-bottom: var(--space-2);
        }

        .benefit-content p {
            color: var(--color-gray-300);
            line-height: 1.5;
        }

        .provider-cta {
            display: flex;
            gap: var(--space-4);
            flex-wrap: wrap;
        }

        .analytics-dashboard {
            background: var(--color-light);
            border-radius: var(--radius-2xl);
            padding: var(--space-8);
            color: var(--color-gray-900);
            box-shadow: var(--shadow-2xl);
        }

        .dashboard-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--space-8);
            padding-bottom: var(--space-4);
            border-bottom: 1px solid var(--color-gray-200);
        }

        .dashboard-header h3 {
            font-size: 1.25rem;
            font-weight: 600;
        }

        .model-name {
            background: var(--color-primary);
            color: white;
            padding: var(--space-2) var(--space-4);
            border-radius: var(--radius-lg);
            font-size: 0.875rem;
            font-weight: 500;
        }

        .performance-metrics {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: var(--space-6);
            margin-bottom: var(--space-8);
        }

        .metric {
            background: var(--color-gray-50);
            padding: var(--space-4);
            border-radius: var(--radius-lg);
            text-align: center;
        }

        .metric-label {
            font-size: 0.875rem;
            color: var(--color-gray-500);
            margin-bottom: var(--space-2);
        }

        .metric-value {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--color-gray-900);
            margin-bottom: var(--space-1);
        }

        .metric-trend {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: var(--space-1);
            font-size: 0.875rem;
            color: var(--color-primary);
            font-weight: 500;
        }

        .category-breakdown {
            display: flex;
            flex-direction: column;
            gap: var(--space-4);
        }

        .category-item {
            display: flex;
            align-items: center;
            gap: var(--space-4);
        }

        .category-name {
            font-size: 0.875rem;
            font-weight: 500;
            color: var(--color-gray-700);
            min-width: 100px;
        }

        .category-bar {
            flex: 1;
            height: 8px;
            background: var(--color-gray-200);
            border-radius: var(--radius-sm);
            overflow: hidden;
        }

        .category-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--color-primary), #059669);
            border-radius: var(--radius-sm);
        }

        .category-score {
            font-size: 0.875rem;
            font-weight: 600;
            color: var(--color-gray-700);
            min-width: 40px;
            text-align: right;
        }

        /* Final CTA Section */
        .final-cta {
            padding: var(--space-24) 0;
            background: linear-gradient(135deg, var(--color-dark) 0%, var(--color-dark-lighter) 100%);
            text-align: center;
        }

        .cta-title {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: var(--space-6);
            letter-spacing: -0.025em;
        }

        .cta-description {
            font-size: 1.25rem;
            color: var(--color-gray-300);
            max-width: 600px;
            margin: 0 auto var(--space-12);
            line-height: 1.6;
        }

        .cta-options {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: var(--space-8);
            max-width: 800px;
            margin: 0 auto var(--space-12);
        }

        .cta-option {
            background: var(--color-light);
            border-radius: var(--radius-2xl);
            padding: var(--space-8);
            color: var(--color-gray-900);
            box-shadow: var(--shadow-lg);
        }

        .option-icon {
            background: linear-gradient(135deg, var(--color-primary), #059669);
            color: white;
            width: 60px;
            height: 60px;
            border-radius: var(--radius-xl);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            margin: 0 auto var(--space-6);
        }

        .cta-option h3 {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: var(--space-4);
            color: var(--color-gray-900);
        }

        .cta-option p {
            color: var(--color-gray-600);
            margin-bottom: var(--space-6);
            line-height: 1.5;
        }

        .trust-indicators {
            display: flex;
            justify-content: center;
            gap: var(--space-8);
            flex-wrap: wrap;
        }

        .trust-item {
            display: flex;
            align-items: center;
            gap: var(--space-2);
            color: var(--color-gray-300);
            font-size: 0.875rem;
            font-weight: 500;
        }

        .trust-item i {
            color: var(--color-primary);
        }

        /* Footer */
        .footer {
            background: var(--color-gray-900);
            padding: var(--space-16) 0 var(--space-8);
            border-top: 1px solid var(--color-gray-800);
        }

        .footer-content {
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: var(--space-16);
            margin-bottom: var(--space-12);
        }

        .footer-logo {
            font-size: 1.75rem;
            font-weight: 700;
            color: var(--color-primary);
            text-decoration: none;
            margin-bottom: var(--space-4);
            display: block;
        }

        .footer-description {
            color: var(--color-gray-400);
            line-height: 1.6;
            margin-bottom: var(--space-6);
        }

        .social-links {
            display: flex;
            gap: var(--space-4);
        }

        .social-link {
            background: var(--color-gray-800);
            color: var(--color-gray-400);
            width: 40px;
            height: 40px;
            border-radius: var(--radius-lg);
            display: flex;
            align-items: center;
            justify-content: center;
            text-decoration: none;
            transition: all 0.2s ease;
        }

        .social-link:hover {
            background: var(--color-primary);
            color: white;
        }

        .footer-links {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: var(--space-8);
        }

        .link-group h4 {
            font-size: 1rem;
            font-weight: 600;
            color: var(--color-light);
            margin-bottom: var(--space-4);
        }

        .link-group a {
            display: block;
            color: var(--color-gray-400);
            text-decoration: none;
            margin-bottom: var(--space-3);
            transition: color 0.2s ease;
        }

        .link-group a:hover {
            color: var(--color-primary);
        }

        .footer-bottom {
            text-align: center;
            padding-top: var(--space-8);
            border-top: 1px solid var(--color-gray-800);
            color: var(--color-gray-500);
        }

        .footer-bottom p {
            margin-bottom: var(--space-2);
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .impact-grid,
            .provider-content {
                grid-template-columns: 1fr;
                gap: var(--space-12);
            }

            .impact-stats {
                grid-template-columns: 1fr;
            }

            .cta-options {
                grid-template-columns: 1fr;
            }

            .footer-content {
                grid-template-columns: 1fr;
                gap: var(--space-12);
            }

            .footer-links {
                grid-template-columns: repeat(2, 1fr);
                gap: var(--space-6);
            }

            .trust-indicators {
                gap: var(--space-4);
            }

            .cta-title,
            .provider-title {
                font-size: 2.5rem;
            }
        }
    </style>

    <!-- JavaScript for Interactivity -->
    <script>
        // Mobile menu toggle
        document.addEventListener('DOMContentLoaded', function() {
            const mobileMenuBtn = document.querySelector('.mobile-menu-btn');
            const navLinks = document.querySelector('.nav-links');

            if (mobileMenuBtn) {
                mobileMenuBtn.addEventListener('click', function() {
                    navLinks.style.display = navLinks.style.display === 'flex' ? 'none' : 'flex';
                });
            }

            // Smooth scrolling for anchor links
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });

            // Filter buttons functionality
            const filterBtns = document.querySelectorAll('.filter-btn');
            filterBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    filterBtns.forEach(b => b.classList.remove('active'));
                    this.classList.add('active');
                });
            });

            // Add scroll effect to navbar
            window.addEventListener('scroll', function() {
                const navbar = document.querySelector('.navbar');
                if (window.scrollY > 100) {
                    navbar.style.background = 'rgba(15, 23, 42, 0.98)';
                } else {
                    navbar.style.background = 'rgba(15, 23, 42, 0.95)';
                }
            });

            // Animated number counting
            function animateNumbers() {
                const numbers = document.querySelectorAll('.stat-number');
                numbers.forEach(number => {
                    const target = parseInt(number.textContent.replace(/,/g, ''));
                    let current = 0;
                    const increment = target / 100;
                    const timer = setInterval(() => {
                        current += increment;
                        if (current >= target) {
                            current = target;
                            clearInterval(timer);
                        }
                        number.textContent = Math.floor(current).toLocaleString();
                    }, 20);
                });
            }

            // Intersection Observer for animations
            const observerOptions = {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            };

            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.animationPlayState = 'running';
                        if (entry.target.classList.contains('hero-stats')) {
                            setTimeout(animateNumbers, 1000);
                        }
                    }
                });
            }, observerOptions);

            // Observe elements for animation
            document.querySelectorAll('.step-card, .hero-stats, .section-header').forEach(el => {
                observer.observe(el);
            });

            // Parallax effect for floating elements
            window.addEventListener('scroll', function() {
                const scrolled = window.pageYOffset;
                const parallax = document.querySelectorAll('.floating-element');
                const speed = 0.5;

                parallax.forEach(element => {
                    const yPos = -(scrolled * speed);
                    element.style.transform = `translateY(${yPos}px)`;
                });
            });

            // Micro-interactions for buttons
            document.querySelectorAll('.btn').forEach(btn => {
                btn.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-2px) scale(1.02)';
                });

                btn.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });

            // Card hover effects
            document.querySelectorAll('.step-card, .category-card, .stat-card').forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-8px) scale(1.02)';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });
        });
    </script>
</body>
</html>