<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Huval - Revolutionary AI Evaluation Platform</title>
    <meta name="description" content="Community-driven AI evaluation platform that breaks away from biased datasets. Create topics, evaluate AI responses, and discover the best performing AI models through real user feedback.">

    <!-- CDN Resources -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" integrity="sha512-DTOQO9RWCH3ppGqcWaEA1BIZOC6xxalwEsw9c2QQeAIftl+Vegovlnee1c9QX4TctnWMn13TZye+giMm8e2LwA==" crossorigin="anonymous" referrerpolicy="no-referrer">

    <!-- Custom Styles -->
    <style>
        :root {
            /* Color System */
            --color-primary: #10B981;
            --color-secondary: #6366F1;
            --color-accent: #F59E0B;
            --color-dark: #0F172A;
            --color-dark-lighter: #1E293B;
            --color-light: #ffffff;
            --color-gray-50: #F8FAFC;
            --color-gray-100: #F1F5F9;
            --color-gray-200: #E2E8F0;
            --color-gray-300: #CBD5E1;
            --color-gray-400: #94A3B8;
            --color-gray-500: #64748B;
            --color-gray-600: #475569;
            --color-gray-700: #334155;
            --color-gray-800: #1E293B;
            --color-gray-900: #0F172A;

            /* Typography */
            --font-primary: 'Inter', system-ui, -apple-system, sans-serif;

            /* Spacing */
            --space-1: 0.25rem;
            --space-2: 0.5rem;
            --space-3: 0.75rem;
            --space-4: 1rem;
            --space-5: 1.25rem;
            --space-6: 1.5rem;
            --space-8: 2rem;
            --space-10: 2.5rem;
            --space-12: 3rem;
            --space-16: 4rem;
            --space-20: 5rem;
            --space-24: 6rem;

            /* Border Radius */
            --radius-sm: 0.375rem;
            --radius-md: 0.5rem;
            --radius-lg: 0.75rem;
            --radius-xl: 1rem;
            --radius-2xl: 1.5rem;
            --radius-3xl: 2rem;

            /* Shadows */
            --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
            --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);
            --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1);
            --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: var(--font-primary);
            background-color: var(--color-dark);
            color: var(--color-light);
            line-height: 1.6;
            overflow-x: hidden;
        }

        /* Enhanced Navigation */
        .navbar {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            background: rgba(15, 23, 42, 0.85);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(16, 185, 129, 0.1);
            padding: var(--space-5) 0;
            transition: all 0.3s ease;
        }

        .navbar::before {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, var(--color-primary), transparent);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .navbar.scrolled::before {
            opacity: 1;
        }

        .nav-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 var(--space-8);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            font-size: 2rem;
            font-weight: 900;
            background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-decoration: none;
            letter-spacing: -0.04em;
            position: relative;
        }

        .logo::after {
            content: '';
            position: absolute;
            bottom: -4px;
            left: 0;
            width: 0;
            height: 2px;
            background: linear-gradient(90deg, var(--color-primary), var(--color-secondary));
            transition: width 0.3s ease;
        }

        .logo:hover::after {
            width: 100%;
        }

        .nav-links {
            display: flex;
            list-style: none;
            gap: var(--space-10);
            align-items: center;
            background: rgba(255, 255, 255, 0.05);
            padding: var(--space-3) var(--space-6);
            border-radius: var(--radius-3xl);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .nav-links a {
            color: var(--color-gray-300);
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            font-size: 1rem;
            padding: var(--space-2) var(--space-4);
            border-radius: var(--radius-lg);
            position: relative;
            overflow: hidden;
        }

        .nav-links a::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(16, 185, 129, 0.1), transparent);
            transition: left 0.5s ease;
        }

        .nav-links a:hover::before {
            left: 100%;
        }

        .nav-links a:hover {
            color: var(--color-primary);
            background: rgba(16, 185, 129, 0.1);
            transform: translateY(-1px);
        }

        .nav-cta {
            display: flex;
            gap: var(--space-4);
            align-items: center;
        }

        .btn {
            padding: var(--space-4) var(--space-8);
            border-radius: var(--radius-2xl);
            text-decoration: none;
            font-weight: 700;
            font-size: 1rem;
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            border: none;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            gap: var(--space-3);
            position: relative;
            overflow: hidden;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.6s ease;
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
            color: white;
            box-shadow: 0 8px 25px rgba(16, 185, 129, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .btn-primary:hover {
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 15px 40px rgba(16, 185, 129, 0.4);
        }

        .btn-secondary {
            background: rgba(255, 255, 255, 0.05);
            color: var(--color-gray-300);
            border: 1px solid rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
        }

        .btn-secondary:hover {
            background: rgba(255, 255, 255, 0.1);
            color: var(--color-light);
            transform: translateY(-2px) scale(1.02);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }

        /* Mobile Menu */
        .mobile-menu-btn {
            display: none;
            background: none;
            border: none;
            color: var(--color-light);
            font-size: 1.5rem;
            cursor: pointer;
        }

        @media (max-width: 768px) {
            .nav-links {
                display: none;
            }

            .mobile-menu-btn {
                display: block;
            }

            .nav-cta {
                gap: var(--space-2);
            }

            .btn {
                padding: var(--space-2) var(--space-4);
                font-size: 0.85rem;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <a href="#" class="logo">huval</a>

            <ul class="nav-links">
                <li><a href="leaderboard.html">Leaderboard</a></li>
                <li><a href="topics.html">Topics</a></li>
                <li><a href="#categories">Categories</a></li>
                <li><a href="#community">Community</a></li>
                <li><a href="about.html">About</a></li>
            </ul>

            <div class="nav-cta">
                <a href="login.html" class="btn btn-secondary">Sign In</a>
                <a href="register.html" class="btn btn-primary">
                    <i class="fas fa-rocket"></i>
                    Join Now
                </a>
            </div>

            <button class="mobile-menu-btn">
                <i class="fas fa-bars"></i>
            </button>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero">
        <div class="floating-elements">
            <div class="floating-element"></div>
            <div class="floating-element"></div>
            <div class="floating-element"></div>
        </div>
        <div class="hero-container">
            <div class="hero-content">
                <div class="hero-badge">
                    <i class="fas fa-star"></i>
                    <span>Community-Driven AI Evaluation</span>
                </div>

                <h1 class="hero-title">
                    Revolutionizing AI Evaluation
                    <span class="gradient-text">Through Real Community</span>
                </h1>

                <p class="hero-description">
                    Break free from biased datasets. Real users. Real questions. Real results.
                </p>

                <div class="hero-stats">
                    <div class="stat-item">
                        <div class="stat-number">12,847</div>
                        <div class="stat-label">Topics Evaluated</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">156,392</div>
                        <div class="stat-label">AI Responses</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">8,234</div>
                        <div class="stat-label">Active Evaluators</div>
                    </div>
                </div>

                <div class="hero-cta">
                    <a href="create-topic.html" class="btn btn-primary btn-large">
                        <i class="fas fa-plus-circle"></i>
                        Start Evaluating
                    </a>
                    <a href="leaderboard.html" class="btn btn-secondary btn-large">
                        <i class="fas fa-trophy"></i>
                        View Leaderboard
                    </a>
                </div>
            </div>

            <div class="hero-visual">
                <div class="ai-comparison-card">
                    <div class="card-header">
                        <h3>Live AI Comparison</h3>
                        <span class="live-indicator">
                            <i class="fas fa-circle"></i>
                            Live
                        </span>
                    </div>

                    <div class="comparison-item">
                        <div class="ai-response">
                            <div class="ai-name">GPT-4</div>
                            <div class="response-preview">"The solution involves implementing a recursive algorithm..."</div>
                            <div class="vote-count">
                                <i class="fas fa-thumbs-up"></i>
                                847 votes
                            </div>
                        </div>
                    </div>

                    <div class="comparison-item">
                        <div class="ai-response">
                            <div class="ai-name">Claude-3</div>
                            <div class="response-preview">"I'd approach this by breaking down the problem into..."</div>
                            <div class="vote-count">
                                <i class="fas fa-thumbs-up"></i>
                                923 votes
                            </div>
                        </div>
                    </div>

                    <div class="comparison-item winner">
                        <div class="ai-response">
                            <div class="ai-name">Gemini Pro</div>
                            <div class="response-preview">"Here's a comprehensive solution with examples..."</div>
                            <div class="vote-count">
                                <i class="fas fa-crown"></i>
                                1,247 votes
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <style>
        /* Hero Section Styles */
        .hero {
            min-height: 120vh;
            display: flex;
            align-items: center;
            background: linear-gradient(135deg, var(--color-dark) 0%, var(--color-dark-lighter) 100%);
            position: relative;
            overflow: hidden;
            padding: var(--space-24) 0;
            margin-top: 80px;
        }

        .hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at 20% 80%, rgba(16, 185, 129, 0.15) 0%, transparent 60%),
                        radial-gradient(circle at 80% 20%, rgba(99, 102, 241, 0.15) 0%, transparent 60%),
                        radial-gradient(circle at 50% 50%, rgba(245, 158, 11, 0.08) 0%, transparent 70%);
            pointer-events: none;
            animation: gradientShift 8s ease-in-out infinite;
        }

        @keyframes gradientShift {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.8; }
        }

        .hero-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 var(--space-8);
            display: grid;
            grid-template-columns: 1.2fr 0.8fr;
            gap: var(--space-24);
            align-items: center;
            position: relative;
            z-index: 1;
        }

        .hero-badge {
            display: inline-flex;
            align-items: center;
            gap: var(--space-3);
            background: rgba(16, 185, 129, 0.12);
            border: 1px solid rgba(16, 185, 129, 0.25);
            color: var(--color-primary);
            padding: var(--space-4) var(--space-6);
            border-radius: var(--radius-3xl);
            font-size: 1rem;
            font-weight: 600;
            margin-bottom: var(--space-10);
            backdrop-filter: blur(10px);
            animation: badgePulse 3s ease-in-out infinite;
            transition: all 0.3s ease;
        }

        .hero-badge:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(16, 185, 129, 0.2);
        }

        @keyframes badgePulse {
            0%, 100% { box-shadow: 0 0 0 0 rgba(16, 185, 129, 0.4); }
            50% { box-shadow: 0 0 0 10px rgba(16, 185, 129, 0); }
        }

        .hero-title {
            font-size: 4.5rem;
            font-weight: 800;
            line-height: 1.05;
            margin-bottom: var(--space-12);
            letter-spacing: -0.04em;
            animation: titleSlideUp 1s ease-out 0.3s both;
        }

        @keyframes titleSlideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .gradient-text {
            background: linear-gradient(135deg, var(--color-primary), var(--color-secondary), var(--color-accent));
            background-size: 200% 200%;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: gradientFlow 4s ease-in-out infinite;
        }

        @keyframes gradientFlow {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        .hero-description {
            font-size: 1.4rem;
            color: var(--color-gray-300);
            margin-bottom: var(--space-16);
            line-height: 1.7;
            animation: descriptionFadeIn 1s ease-out 0.6s both;
            max-width: 90%;
        }

        @keyframes descriptionFadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .hero-stats {
            display: flex;
            gap: var(--space-12);
            margin-bottom: var(--space-20);
            animation: statsSlideUp 1s ease-out 0.9s both;
        }

        .stat-item {
            text-align: left;
            position: relative;
            padding: var(--space-6);
            border-radius: var(--radius-xl);
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .stat-item:hover {
            transform: translateY(-5px);
            background: rgba(255, 255, 255, 0.08);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 800;
            color: var(--color-primary);
            margin-bottom: var(--space-2);
            display: block;
            animation: numberCount 2s ease-out 1.2s both;
        }

        .stat-label {
            font-size: 1rem;
            color: var(--color-gray-300);
            font-weight: 600;
            letter-spacing: 0.02em;
        }

        @keyframes statsSlideUp {
            from {
                opacity: 0;
                transform: translateY(40px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes numberCount {
            from {
                opacity: 0;
                transform: scale(0.5);
            }
            to {
                opacity: 1;
                transform: scale(1);
            }
        }

        .hero-cta {
            display: flex;
            gap: var(--space-6);
            flex-wrap: wrap;
            animation: ctaFadeIn 1s ease-out 1.2s both;
        }

        .btn-large {
            padding: var(--space-5) var(--space-10);
            font-size: 1.2rem;
            font-weight: 700;
            border-radius: var(--radius-2xl);
            position: relative;
            overflow: hidden;
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        }

        .btn-large::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.6s ease;
        }

        .btn-large:hover::before {
            left: 100%;
        }

        .btn-large:hover {
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
        }

        @keyframes ctaFadeIn {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* AI Comparison Card */
        .hero-visual {
            display: flex;
            justify-content: center;
            align-items: center;
            animation: visualSlideIn 1s ease-out 0.6s both;
        }

        .ai-comparison-card {
            background: var(--color-light);
            border-radius: var(--radius-3xl);
            padding: var(--space-10);
            box-shadow: 0 25px 60px rgba(0, 0, 0, 0.3);
            width: 100%;
            max-width: 480px;
            color: var(--color-gray-900);
            position: relative;
            overflow: hidden;
            transition: all 0.4s ease;
        }

        .ai-comparison-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--color-primary), var(--color-secondary), var(--color-accent));
            background-size: 200% 100%;
            animation: gradientSlide 3s ease-in-out infinite;
        }

        .ai-comparison-card:hover {
            transform: translateY(-10px) scale(1.02);
            box-shadow: 0 35px 80px rgba(0, 0, 0, 0.4);
        }

        @keyframes visualSlideIn {
            from {
                opacity: 0;
                transform: translateX(50px) scale(0.9);
            }
            to {
                opacity: 1;
                transform: translateX(0) scale(1);
            }
        }

        @keyframes gradientSlide {
            0%, 100% { background-position: 0% 0%; }
            50% { background-position: 200% 0%; }
        }

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--space-6);
            padding-bottom: var(--space-4);
            border-bottom: 1px solid var(--color-gray-200);
        }

        .card-header h3 {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--color-gray-900);
        }

        .live-indicator {
            display: flex;
            align-items: center;
            gap: var(--space-2);
            color: var(--color-primary);
            font-size: 0.875rem;
            font-weight: 500;
        }

        .live-indicator i {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .comparison-item {
            margin-bottom: var(--space-4);
            padding: var(--space-4);
            border-radius: var(--radius-lg);
            background: var(--color-gray-50);
            transition: all 0.2s ease;
        }

        .comparison-item:hover {
            background: var(--color-gray-100);
            transform: translateY(-1px);
        }

        .comparison-item.winner {
            background: linear-gradient(135deg, rgba(16, 185, 129, 0.1), rgba(16, 185, 129, 0.05));
            border: 1px solid rgba(16, 185, 129, 0.2);
        }

        .ai-name {
            font-weight: 600;
            color: var(--color-gray-900);
            margin-bottom: var(--space-2);
        }

        .response-preview {
            font-size: 0.875rem;
            color: var(--color-gray-600);
            margin-bottom: var(--space-3);
            line-height: 1.4;
        }

        .vote-count {
            display: flex;
            align-items: center;
            gap: var(--space-2);
            font-size: 0.875rem;
            font-weight: 500;
            color: var(--color-primary);
        }

        .comparison-item.winner .vote-count {
            color: var(--color-accent);
        }

        /* Floating Elements */
        .floating-elements {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            pointer-events: none;
            overflow: hidden;
        }

        .floating-element {
            position: absolute;
            background: linear-gradient(135deg, rgba(16, 185, 129, 0.1), rgba(99, 102, 241, 0.1));
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }

        .floating-element:nth-child(1) {
            width: 60px;
            height: 60px;
            top: 20%;
            left: 10%;
            animation-delay: 0s;
        }

        .floating-element:nth-child(2) {
            width: 40px;
            height: 40px;
            top: 60%;
            right: 15%;
            animation-delay: 2s;
        }

        .floating-element:nth-child(3) {
            width: 80px;
            height: 80px;
            bottom: 30%;
            left: 20%;
            animation-delay: 4s;
        }

        @keyframes float {
            0%, 100% {
                transform: translateY(0px) rotate(0deg);
                opacity: 0.3;
            }
            50% {
                transform: translateY(-20px) rotate(180deg);
                opacity: 0.6;
            }
        }

        /* Enhanced Spacing */
        .section-spacing {
            padding: var(--space-24) 0;
        }

        .section-spacing-large {
            padding: var(--space-24) 0 var(--space-24) 0;
        }

        @media (max-width: 768px) {
            .hero-container {
                grid-template-columns: 1fr;
                gap: var(--space-16);
                text-align: center;
            }

            .hero-title {
                font-size: 3rem;
            }

            .hero-description {
                font-size: 1.2rem;
            }

            .hero-stats {
                justify-content: center;
                gap: var(--space-8);
                grid-template-columns: repeat(2, 1fr);
                display: grid;
            }

            .hero-cta {
                justify-content: center;
                flex-direction: column;
                align-items: center;
            }

            .section-title {
                font-size: 3rem;
            }
        }
    </style>

    <!-- How It Works Section -->
    <section class="how-it-works">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">How Huval Works</h2>
                <p class="section-description">
                    Real users. Real questions. Unbiased results.
                </p>
            </div>

            <div class="dynamic-steps-layout">
                <!-- Step 1: Large Featured Card -->
                <div class="step-card step-card-large step-card-primary">
                    <div class="step-number-large">01</div>
                    <div class="step-content-wrapper">
                        <div class="step-icon-large">
                            <i class="fas fa-lightbulb"></i>
                        </div>
                        <div class="step-text-content">
                            <h3 class="step-title-large">Create Topics</h3>
                            <p class="step-description-large">
                                Submit real questions across coding, knowledge, and creative categories.
                            </p>
                        </div>
                    </div>
                    <div class="step-visual-large">
                        <div class="topic-creation-demo">
                            <div class="demo-header">
                                <div class="demo-title">New Topic</div>
                                <div class="demo-status">Draft</div>
                            </div>
                            <div class="demo-category">
                                <span class="category-tag active">Coding</span>
                                <span class="category-tag">Knowledge</span>
                                <span class="category-tag">Creative</span>
                            </div>
                            <div class="demo-question">
                                <div class="question-text">"How to implement a binary search tree in Python?"</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Step 2: Compact Side Card -->
                <div class="step-card step-card-compact step-card-secondary">
                    <div class="step-number-compact">02</div>
                    <div class="step-icon-compact">
                        <i class="fas fa-robot"></i>
                    </div>
                    <h3 class="step-title-compact">AI Responses</h3>
                    <p class="step-description-compact">
                        Multiple AI systems respond anonymously.
                    </p>
                    <div class="ai-avatars">
                        <div class="ai-avatar ai-1"></div>
                        <div class="ai-avatar ai-2"></div>
                        <div class="ai-avatar ai-3"></div>
                        <div class="ai-avatar ai-more">+4</div>
                    </div>
                </div>

                <!-- Step 3: Wide Horizontal Card -->
                <div class="step-card step-card-wide step-card-tertiary">
                    <div class="step-number-wide">03</div>
                    <div class="step-content-horizontal">
                        <div class="step-left">
                            <div class="step-icon-wide">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="step-text-wide">
                                <h3 class="step-title-wide">Community Evaluation</h3>
                                <p class="step-description-wide">
                                    Community votes on quality and accuracy.
                                </p>
                            </div>
                        </div>
                        <div class="step-right">
                            <div class="voting-demo">
                                <div class="vote-option vote-up">
                                    <i class="fas fa-thumbs-up"></i>
                                    <span class="vote-count">1,247</span>
                                </div>
                                <div class="vote-option vote-down">
                                    <i class="fas fa-thumbs-down"></i>
                                    <span class="vote-count">23</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Step 4: Floating Overlay Card -->
                <div class="step-card step-card-floating step-card-accent">
                    <div class="step-number-floating">04</div>
                    <div class="floating-content">
                        <div class="step-icon-floating">
                            <i class="fas fa-trophy"></i>
                        </div>
                        <h3 class="step-title-floating">Global Rankings</h3>
                        <p class="step-description-floating">
                            Real-world performance rankings.
                        </p>
                        <div class="mini-leaderboard">
                            <div class="leader-item winner">
                                <span class="leader-rank">1</span>
                                <span class="leader-name">Claude-3</span>
                                <span class="leader-score">94.2%</span>
                            </div>
                            <div class="leader-item">
                                <span class="leader-rank">2</span>
                                <span class="leader-name">GPT-4</span>
                                <span class="leader-score">92.8%</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <style>
        /* How It Works Section */
        .how-it-works {
            padding: var(--space-24) 0 var(--space-24) 0;
            background: var(--color-dark-lighter);
            position: relative;
            overflow: hidden;
        }

        .how-it-works::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at 30% 20%, rgba(16, 185, 129, 0.05) 0%, transparent 50%),
                        radial-gradient(circle at 70% 80%, rgba(99, 102, 241, 0.05) 0%, transparent 50%);
            pointer-events: none;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 var(--space-8);
            position: relative;
            z-index: 1;
        }

        .section-header {
            text-align: center;
            margin-bottom: var(--space-24);
            animation: sectionHeaderFadeIn 1s ease-out;
        }

        .section-title {
            font-size: 4rem;
            font-weight: 800;
            margin-bottom: var(--space-8);
            letter-spacing: -0.04em;
            background: linear-gradient(135deg, var(--color-light), var(--color-gray-300));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .section-description {
            font-size: 1.4rem;
            color: var(--color-gray-300);
            max-width: 700px;
            margin: 0 auto;
            line-height: 1.7;
            opacity: 0.9;
        }

        @keyframes sectionHeaderFadeIn {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Dynamic Steps Layout */
        .dynamic-steps-layout {
            display: grid;
            grid-template-columns: 2fr 1fr;
            grid-template-rows: auto auto;
            gap: var(--space-8);
            position: relative;
            min-height: 800px;
        }

        /* Step 1: Large Featured Card */
        .step-card-large {
            grid-column: 1 / 2;
            grid-row: 1 / 3;
            background: linear-gradient(135deg, var(--color-light) 0%, #f8fafc 100%);
            border-radius: var(--radius-3xl);
            padding: var(--space-16);
            position: relative;
            overflow: hidden;
            box-shadow: 0 25px 60px rgba(0, 0, 0, 0.12);
            border: 1px solid rgba(16, 185, 129, 0.1);
        }

        .step-card-large::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 8px;
            background: linear-gradient(90deg, var(--color-primary), var(--color-secondary), var(--color-accent));
        }

        .step-number-large {
            position: absolute;
            top: var(--space-8);
            right: var(--space-8);
            background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
            color: white;
            width: 80px;
            height: 80px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 900;
            font-size: 2rem;
            box-shadow: 0 15px 35px rgba(16, 185, 129, 0.3);
        }

        .step-content-wrapper {
            display: flex;
            align-items: flex-start;
            gap: var(--space-8);
            margin-bottom: var(--space-12);
        }

        .step-icon-large {
            background: linear-gradient(135deg, rgba(16, 185, 129, 0.15), rgba(99, 102, 241, 0.15));
            color: var(--color-primary);
            width: 100px;
            height: 100px;
            border-radius: var(--radius-2xl);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.5rem;
            border: 3px solid rgba(16, 185, 129, 0.2);
        }

        .step-title-large {
            font-size: 2.5rem;
            font-weight: 800;
            color: var(--color-gray-900);
            margin-bottom: var(--space-4);
            letter-spacing: -0.02em;
        }

        .step-description-large {
            font-size: 1.3rem;
            color: var(--color-gray-600);
            line-height: 1.6;
        }

        .step-visual-large {
            background: linear-gradient(135deg, var(--color-gray-50), var(--color-gray-100));
            border-radius: var(--radius-2xl);
            padding: var(--space-8);
            border: 2px solid var(--color-gray-200);
        }

        /* Step 2: Compact Side Card */
        .step-card-compact {
            grid-column: 2 / 3;
            grid-row: 1 / 2;
            background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
            color: white;
            border-radius: var(--radius-3xl);
            padding: var(--space-10);
            position: relative;
            transform: translateY(-40px);
            box-shadow: 0 20px 50px rgba(0, 0, 0, 0.3);
        }

        .step-number-compact {
            position: absolute;
            top: var(--space-6);
            right: var(--space-6);
            background: rgba(255, 255, 255, 0.2);
            color: white;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 800;
            font-size: 1.2rem;
            backdrop-filter: blur(10px);
        }

        .step-icon-compact {
            background: rgba(16, 185, 129, 0.2);
            color: var(--color-primary);
            width: 70px;
            height: 70px;
            border-radius: var(--radius-xl);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.8rem;
            margin-bottom: var(--space-6);
            border: 2px solid rgba(16, 185, 129, 0.3);
        }

        .step-title-compact {
            font-size: 1.8rem;
            font-weight: 700;
            margin-bottom: var(--space-4);
        }

        .step-description-compact {
            font-size: 1.1rem;
            opacity: 0.9;
            margin-bottom: var(--space-6);
            line-height: 1.5;
        }

        .ai-avatars {
            display: flex;
            gap: var(--space-2);
        }

        .ai-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 0.8rem;
            border: 2px solid rgba(255, 255, 255, 0.3);
        }

        .ai-1 { background: linear-gradient(135deg, #10B981, #059669); }
        .ai-2 { background: linear-gradient(135deg, #6366F1, #4F46E5); }
        .ai-3 { background: linear-gradient(135deg, #F59E0B, #D97706); }
        .ai-more { background: rgba(255, 255, 255, 0.2); backdrop-filter: blur(10px); }

        /* Step 3: Wide Horizontal Card */
        .step-card-wide {
            grid-column: 1 / 3;
            grid-row: 2 / 3;
            background: linear-gradient(135deg, #6366F1 0%, #4F46E5 100%);
            color: white;
            border-radius: var(--radius-3xl);
            padding: var(--space-12);
            position: relative;
            margin-top: var(--space-8);
            box-shadow: 0 25px 60px rgba(99, 102, 241, 0.3);
        }

        .step-number-wide {
            position: absolute;
            top: var(--space-8);
            left: var(--space-8);
            background: rgba(255, 255, 255, 0.2);
            color: white;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 800;
            font-size: 1.4rem;
            backdrop-filter: blur(10px);
        }

        .step-content-horizontal {
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: var(--space-12);
        }

        .step-left {
            display: flex;
            align-items: center;
            gap: var(--space-8);
        }

        .step-icon-wide {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            width: 80px;
            height: 80px;
            border-radius: var(--radius-xl);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            backdrop-filter: blur(10px);
        }

        .step-title-wide {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: var(--space-3);
        }

        .step-description-wide {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .voting-demo {
            display: flex;
            gap: var(--space-6);
        }

        .vote-option {
            background: rgba(255, 255, 255, 0.15);
            border-radius: var(--radius-xl);
            padding: var(--space-4) var(--space-6);
            display: flex;
            align-items: center;
            gap: var(--space-3);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .vote-up { border-left: 4px solid #10B981; }
        .vote-down { border-left: 4px solid #EF4444; }

        /* Step 4: Floating Overlay Card */
        .step-card-floating {
            position: absolute;
            top: 50%;
            right: -60px;
            transform: translateY(-50%);
            background: linear-gradient(135deg, #F59E0B 0%, #D97706 100%);
            color: white;
            border-radius: var(--radius-3xl);
            padding: var(--space-10);
            width: 320px;
            box-shadow: 0 30px 70px rgba(245, 158, 11, 0.4);
            z-index: 10;
        }

        .step-number-floating {
            position: absolute;
            top: var(--space-6);
            right: var(--space-6);
            background: rgba(255, 255, 255, 0.2);
            color: white;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 800;
            font-size: 1.2rem;
            backdrop-filter: blur(10px);
        }

        .step-icon-floating {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            width: 70px;
            height: 70px;
            border-radius: var(--radius-xl);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.8rem;
            margin-bottom: var(--space-6);
            backdrop-filter: blur(10px);
        }

        .step-title-floating {
            font-size: 1.6rem;
            font-weight: 700;
            margin-bottom: var(--space-4);
        }

        .step-description-floating {
            font-size: 1rem;
            opacity: 0.9;
            margin-bottom: var(--space-6);
        }

        .mini-leaderboard {
            display: flex;
            flex-direction: column;
            gap: var(--space-3);
        }

        .leader-item {
            background: rgba(255, 255, 255, 0.15);
            border-radius: var(--radius-lg);
            padding: var(--space-3) var(--space-4);
            display: flex;
            justify-content: space-between;
            align-items: center;
            backdrop-filter: blur(10px);
        }

        .leader-item.winner {
            background: rgba(255, 255, 255, 0.25);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .leader-rank {
            font-weight: 800;
            font-size: 1.1rem;
        }

        .leader-name {
            font-weight: 600;
        }

        .leader-score {
            font-weight: 700;
            color: rgba(255, 255, 255, 0.9);
        }

        /* Demo Components */
        .topic-creation-demo {
            background: white;
            border-radius: var(--radius-xl);
            padding: var(--space-6);
            border: 1px solid var(--color-gray-300);
        }

        .demo-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--space-4);
            padding-bottom: var(--space-3);
            border-bottom: 1px solid var(--color-gray-200);
        }

        .demo-title {
            font-weight: 700;
            color: var(--color-gray-900);
            font-size: 1.1rem;
        }

        .demo-status {
            background: rgba(245, 158, 11, 0.1);
            color: var(--color-accent);
            padding: var(--space-1) var(--space-3);
            border-radius: var(--radius-md);
            font-size: 0.85rem;
            font-weight: 600;
        }

        .demo-category {
            display: flex;
            gap: var(--space-2);
            margin-bottom: var(--space-4);
        }

        .category-tag {
            padding: var(--space-2) var(--space-4);
            border-radius: var(--radius-lg);
            font-size: 0.9rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            border: 1px solid var(--color-gray-300);
            background: white;
            color: var(--color-gray-600);
        }

        .category-tag.active {
            background: var(--color-primary);
            color: white;
            border-color: var(--color-primary);
        }

        .demo-question {
            background: var(--color-gray-50);
            border-radius: var(--radius-lg);
            padding: var(--space-4);
            border-left: 4px solid var(--color-primary);
        }

        .question-text {
            font-style: italic;
            color: var(--color-gray-700);
            font-size: 1rem;
            line-height: 1.5;
        }

        /* Animations for Dynamic Cards */
        .step-card-large {
            animation: slideInLeft 1s ease-out 0.2s both;
        }

        .step-card-compact {
            animation: slideInRight 1s ease-out 0.4s both;
        }

        .step-card-wide {
            animation: slideInUp 1s ease-out 0.6s both;
        }

        .step-card-floating {
            animation: floatIn 1s ease-out 0.8s both;
        }

        @keyframes slideInLeft {
            from {
                opacity: 0;
                transform: translateX(-100px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes slideInRight {
            from {
                opacity: 0;
                transform: translateX(100px) translateY(-40px);
            }
            to {
                opacity: 1;
                transform: translateX(0) translateY(-40px);
            }
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(100px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes floatIn {
            from {
                opacity: 0;
                transform: translateY(-50%) translateX(100px) scale(0.8);
            }
            to {
                opacity: 1;
                transform: translateY(-50%) translateX(0) scale(1);
            }
        }

        /* Hover Effects for Dynamic Cards */
        .step-card-large:hover {
            transform: translateY(-8px) scale(1.01);
            box-shadow: 0 35px 80px rgba(0, 0, 0, 0.15);
        }

        .step-card-compact:hover {
            transform: translateY(-48px) scale(1.05);
            box-shadow: 0 25px 60px rgba(0, 0, 0, 0.4);
        }

        .step-card-wide:hover {
            transform: translateY(-5px) scale(1.01);
            box-shadow: 0 30px 70px rgba(99, 102, 241, 0.4);
        }

        .step-card-floating:hover {
            transform: translateY(-50%) translateX(-10px) scale(1.05);
            box-shadow: 0 40px 90px rgba(245, 158, 11, 0.5);
        }

        /* Responsive Design for Dynamic Layout */
        @media (max-width: 1024px) {
            .dynamic-steps-layout {
                grid-template-columns: 1fr;
                grid-template-rows: auto;
                gap: var(--space-12);
            }

            .step-card-large,
            .step-card-compact,
            .step-card-wide {
                grid-column: 1;
                grid-row: auto;
            }

            .step-card-compact {
                transform: translateY(0);
            }

            .step-card-floating {
                position: relative;
                top: auto;
                right: auto;
                transform: none;
                width: 100%;
                margin-top: var(--space-8);
            }
        }

        .step-number {
            position: absolute;
            top: var(--space-8);
            right: var(--space-8);
            background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
            color: white;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 800;
            font-size: 1.1rem;
            box-shadow: 0 8px 20px rgba(16, 185, 129, 0.3);
            animation: numberPulse 2s ease-in-out infinite;
        }

        .step-icon {
            background: linear-gradient(135deg, rgba(16, 185, 129, 0.15), rgba(99, 102, 241, 0.15));
            color: var(--color-primary);
            width: 80px;
            height: 80px;
            border-radius: var(--radius-2xl);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            margin-bottom: var(--space-8);
            transition: all 0.3s ease;
            border: 2px solid rgba(16, 185, 129, 0.2);
        }

        .step-card:hover .step-icon {
            transform: scale(1.1) rotate(5deg);
            background: linear-gradient(135deg, rgba(16, 185, 129, 0.25), rgba(99, 102, 241, 0.25));
            box-shadow: 0 10px 25px rgba(16, 185, 129, 0.2);
        }

        @keyframes numberPulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        .step-title {
            font-size: 1.75rem;
            font-weight: 700;
            margin-bottom: var(--space-6);
            color: var(--color-gray-900);
            letter-spacing: -0.02em;
        }

        .step-description {
            color: var(--color-gray-600);
            line-height: 1.7;
            margin-bottom: var(--space-8);
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .step-visual {
            background: linear-gradient(135deg, var(--color-gray-50), var(--color-gray-100));
            border-radius: var(--radius-2xl);
            padding: var(--space-6);
            border: 1px solid var(--color-gray-200);
            transition: all 0.3s ease;
        }

        .step-card:hover .step-visual {
            background: linear-gradient(135deg, var(--color-gray-100), var(--color-gray-50));
            transform: scale(1.02);
            box-shadow: inset 0 2px 10px rgba(0, 0, 0, 0.05);
        }

        /* Step Visual Components */
        .mini-form {
            display: flex;
            flex-direction: column;
            gap: var(--space-3);
        }

        .form-field {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .field-label {
            font-size: 0.875rem;
            color: var(--color-gray-500);
            font-weight: 500;
        }

        .field-value {
            font-size: 0.875rem;
            color: var(--color-gray-900);
            font-weight: 500;
        }

        .ai-responses-preview {
            display: flex;
            flex-direction: column;
            gap: var(--space-2);
        }

        .response-item {
            display: flex;
            align-items: center;
            gap: var(--space-3);
        }

        .response-avatar {
            width: 24px;
            height: 24px;
            background: var(--color-primary);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.75rem;
            font-weight: 600;
        }

        .response-text {
            font-size: 0.875rem;
            color: var(--color-gray-600);
        }

        .voting-interface {
            display: flex;
            justify-content: center;
        }

        .vote-buttons {
            display: flex;
            gap: var(--space-4);
        }

        .vote-btn {
            display: flex;
            align-items: center;
            gap: var(--space-2);
            padding: var(--space-2) var(--space-4);
            border: none;
            border-radius: var(--radius-md);
            font-size: 0.875rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .vote-btn.upvote {
            background: rgba(16, 185, 129, 0.1);
            color: var(--color-primary);
        }

        .vote-btn.downvote {
            background: rgba(239, 68, 68, 0.1);
            color: #EF4444;
        }

        .leaderboard-preview {
            display: flex;
            flex-direction: column;
            gap: var(--space-2);
        }

        .rank-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: var(--space-2) var(--space-3);
            background: white;
            border-radius: var(--radius-md);
            font-size: 0.875rem;
        }

        .rank {
            font-weight: 700;
            color: var(--color-primary);
        }

        .ai-name {
            font-weight: 500;
            color: var(--color-gray-900);
        }

        .score {
            font-weight: 600;
            color: var(--color-gray-700);
        }

        @media (max-width: 768px) {
            .section-title {
                font-size: 2.5rem;
            }

            .steps-grid {
                grid-template-columns: 1fr;
                gap: var(--space-6);
            }
        }
    </style>

    <!-- Leaderboard Preview Section -->
    <section class="leaderboard-preview">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">Live AI Leaderboard</h2>
                <p class="section-description">
                    Real-time rankings. Real performance.
                </p>
            </div>

            <div class="leaderboard-dynamic-layout">
                <!-- Winner Spotlight Card -->
                <div class="winner-spotlight-card">
                    <div class="spotlight-header">
                        <div class="crown-icon">
                            <i class="fas fa-crown"></i>
                        </div>
                        <div class="spotlight-title">Current Champion</div>
                    </div>
                    <div class="winner-info">
                        <div class="winner-avatar">
                            <div class="avatar-gradient"></div>
                            <span class="winner-initial">C</span>
                        </div>
                        <div class="winner-details">
                            <h3 class="winner-name">Claude-3 Opus</h3>
                            <p class="winner-provider">Anthropic</p>
                            <div class="winner-score">
                                <span class="score-number">94.2%</span>
                                <span class="score-trend">+2.3% ↗</span>
                            </div>
                        </div>
                    </div>
                    <div class="winner-stats">
                        <div class="stat-item">
                            <span class="stat-value">23,847</span>
                            <span class="stat-label">Total Votes</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-value">156</span>
                            <span class="stat-label">Topics Won</span>
                        </div>
                    </div>
                </div>

                <!-- Compact Rankings Card -->
                <div class="compact-rankings-card">
                    <div class="rankings-header">
                        <h3>Top Performers</h3>
                        <div class="live-indicator">
                            <div class="live-dot"></div>
                            <span>Live</span>
                        </div>
                    </div>
                    <div class="rankings-list">
                        <div class="rank-item rank-2">
                            <span class="rank-number">2</span>
                            <div class="rank-info">
                                <span class="rank-name">GPT-4 Turbo</span>
                                <span class="rank-score">92.8%</span>
                            </div>
                            <div class="rank-trend down">-0.8%</div>
                        </div>
                        <div class="rank-item rank-3">
                            <span class="rank-number">3</span>
                            <div class="rank-info">
                                <span class="rank-name">Gemini Pro</span>
                                <span class="rank-score">89.4%</span>
                            </div>
                            <div class="rank-trend up">+1.2%</div>
                        </div>
                        <div class="rank-item rank-4">
                            <span class="rank-number">4</span>
                            <div class="rank-info">
                                <span class="rank-name">Llama 3</span>
                                <span class="rank-score">87.1%</span>
                            </div>
                            <div class="rank-trend up">+0.5%</div>
                        </div>
                    </div>
                </div>

                <!-- Category Filters Card -->
                <div class="category-filters-card">
                    <h4>Filter by Category</h4>
                    <div class="filter-grid">
                        <button class="filter-chip active">
                            <i class="fas fa-globe"></i>
                            <span>All</span>
                        </button>
                        <button class="filter-chip">
                            <i class="fas fa-code"></i>
                            <span>Coding</span>
                        </button>
                        <button class="filter-chip">
                            <i class="fas fa-brain"></i>
                            <span>Knowledge</span>
                        </button>
                        <button class="filter-chip">
                            <i class="fas fa-pen-fancy"></i>
                            <span>Creative</span>
                        </button>
                    </div>
                </div>

                <!-- CTA Card -->
                <div class="leaderboard-cta-card">
                    <div class="cta-content">
                        <h4>See Full Rankings</h4>
                        <p>Explore detailed performance metrics</p>
                        <a href="leaderboard.html" class="cta-button">
                            <i class="fas fa-chart-line"></i>
                            <span>View Leaderboard</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Categories Showcase Section -->
    <section class="categories-showcase">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">Evaluation Categories</h2>
                <p class="section-description">
                    Diverse domains. Specialized evaluation.
                </p>
            </div>

            <div class="categories-dynamic-layout">
                <!-- Featured Category: Coding -->
                <div class="category-card category-featured">
                    <div class="category-header">
                        <div class="category-icon-large">
                            <i class="fas fa-code"></i>
                        </div>
                        <div class="category-badge">Most Popular</div>
                    </div>
                    <div class="category-content">
                        <h3 class="category-title-large">Coding & Programming</h3>
                        <p class="category-description-large">
                            Algorithm implementation, debugging, and software architecture challenges.
                        </p>
                        <div class="category-metrics">
                            <div class="metric-item">
                                <span class="metric-value">4,234</span>
                                <span class="metric-label">Topics</span>
                            </div>
                            <div class="metric-item">
                                <span class="metric-value">67,892</span>
                                <span class="metric-label">Responses</span>
                            </div>
                            <div class="metric-item">
                                <span class="metric-value">94.2%</span>
                                <span class="metric-label">Accuracy</span>
                            </div>
                        </div>
                        <div class="category-tech-stack">
                            <div class="tech-item python">Python</div>
                            <div class="tech-item javascript">JavaScript</div>
                            <div class="tech-item java">Java</div>
                            <div class="tech-item rust">Rust</div>
                            <div class="tech-more">+8 more</div>
                        </div>
                    </div>
                </div>

                <!-- Compact Categories Grid -->
                <div class="compact-categories">
                    <div class="category-card category-compact category-knowledge">
                        <div class="compact-header">
                            <div class="category-icon-compact">
                                <i class="fas fa-brain"></i>
                            </div>
                            <div class="category-trend">
                                <i class="fas fa-arrow-up"></i>
                                <span>+12%</span>
                            </div>
                        </div>
                        <h4 class="category-title-compact">General Knowledge</h4>
                        <div class="category-stats-compact">
                            <span class="stat-primary">3,567 topics</span>
                            <span class="stat-secondary">52,341 responses</span>
                        </div>
                        <div class="category-preview">
                            <div class="preview-item">Science</div>
                            <div class="preview-item">History</div>
                            <div class="preview-item">Geography</div>
                        </div>
                    </div>

                    <div class="category-card category-compact category-creative">
                        <div class="compact-header">
                            <div class="category-icon-compact">
                                <i class="fas fa-pen-fancy"></i>
                            </div>
                            <div class="category-trend">
                                <i class="fas fa-arrow-up"></i>
                                <span>+8%</span>
                            </div>
                        </div>
                        <h4 class="category-title-compact">Creative Writing</h4>
                        <div class="category-stats-compact">
                            <span class="stat-primary">2,891 topics</span>
                            <span class="stat-secondary">41,267 responses</span>
                        </div>
                        <div class="category-preview">
                            <div class="preview-item">Fiction</div>
                            <div class="preview-item">Poetry</div>
                            <div class="preview-item">Scripts</div>
                        </div>
                    </div>

                    <div class="category-card category-compact category-support">
                        <div class="compact-header">
                            <div class="category-icon-compact">
                                <i class="fas fa-question-circle"></i>
                            </div>
                            <div class="category-trend">
                                <i class="fas fa-arrow-up"></i>
                                <span>+15%</span>
                            </div>
                        </div>
                        <h4 class="category-title-compact">Q&A Support</h4>
                        <div class="category-stats-compact">
                            <span class="stat-primary">2,156 topics</span>
                            <span class="stat-secondary">34,892 responses</span>
                        </div>
                        <div class="category-preview">
                            <div class="preview-item">Support</div>
                            <div class="preview-item">Troubleshooting</div>
                            <div class="preview-item">Guidance</div>
                        </div>
                    </div>
                </div>

                <!-- Floating Stats Card -->
                <div class="category-stats-floating">
                    <div class="floating-stats-header">
                        <h4>Category Performance</h4>
                        <div class="stats-period">Last 30 days</div>
                    </div>
                    <div class="performance-chart">
                        <div class="chart-bar" style="height: 85%">
                            <span class="bar-label">Coding</span>
                        </div>
                        <div class="chart-bar" style="height: 70%">
                            <span class="bar-label">Knowledge</span>
                        </div>
                        <div class="chart-bar" style="height: 60%">
                            <span class="bar-label">Creative</span>
                        </div>
                        <div class="chart-bar" style="height: 55%">
                            <span class="bar-label">Support</span>
                        </div>
                    </div>
                    <div class="chart-legend">
                        <span>Topic Activity</span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <style>
        /* Leaderboard Preview Section */
        .leaderboard-preview {
            padding: var(--space-24) 0;
            background: var(--color-dark);
            position: relative;
            overflow: hidden;
        }

        .leaderboard-preview::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at 70% 30%, rgba(16, 185, 129, 0.08) 0%, transparent 50%),
                        radial-gradient(circle at 30% 70%, rgba(99, 102, 241, 0.08) 0%, transparent 50%);
            pointer-events: none;
        }

        /* Dynamic Leaderboard Layout */
        .leaderboard-dynamic-layout {
            display: grid;
            grid-template-columns: 2fr 1fr;
            grid-template-rows: auto auto;
            gap: var(--space-8);
            position: relative;
            z-index: 1;
        }

        /* Winner Spotlight Card */
        .winner-spotlight-card {
            grid-column: 1 / 2;
            grid-row: 1 / 3;
            background: linear-gradient(135deg, var(--color-light) 0%, #f8fafc 100%);
            border-radius: var(--radius-3xl);
            padding: var(--space-16);
            position: relative;
            overflow: hidden;
            box-shadow: 0 30px 70px rgba(0, 0, 0, 0.15);
            border: 1px solid rgba(245, 158, 11, 0.2);
        }

        .winner-spotlight-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 8px;
            background: linear-gradient(90deg, #F59E0B, #D97706, #B45309);
        }

        .spotlight-header {
            display: flex;
            align-items: center;
            gap: var(--space-4);
            margin-bottom: var(--space-8);
        }

        .crown-icon {
            background: linear-gradient(135deg, #F59E0B, #D97706);
            color: white;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            box-shadow: 0 10px 25px rgba(245, 158, 11, 0.3);
        }

        .spotlight-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--color-gray-900);
        }

        .winner-info {
            display: flex;
            align-items: center;
            gap: var(--space-8);
            margin-bottom: var(--space-12);
        }

        .winner-avatar {
            position: relative;
            width: 120px;
            height: 120px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
            box-shadow: 0 15px 40px rgba(16, 185, 129, 0.3);
        }

        .avatar-gradient {
            position: absolute;
            inset: 4px;
            border-radius: 50%;
            background: linear-gradient(135deg, #10B981, #6366F1);
            opacity: 0.8;
        }

        .winner-initial {
            font-size: 3rem;
            font-weight: 900;
            color: white;
            z-index: 1;
        }

        .winner-name {
            font-size: 2.5rem;
            font-weight: 800;
            color: var(--color-gray-900);
            margin-bottom: var(--space-2);
            letter-spacing: -0.02em;
        }

        .winner-provider {
            font-size: 1.2rem;
            color: var(--color-gray-600);
            margin-bottom: var(--space-4);
        }

        .winner-score {
            display: flex;
            align-items: center;
            gap: var(--space-4);
        }

        .score-number {
            font-size: 3rem;
            font-weight: 900;
            color: var(--color-primary);
        }

        .score-trend {
            background: rgba(16, 185, 129, 0.1);
            color: var(--color-primary);
            padding: var(--space-2) var(--space-4);
            border-radius: var(--radius-lg);
            font-weight: 700;
            font-size: 1.1rem;
        }

        .winner-stats {
            display: flex;
            gap: var(--space-8);
            padding: var(--space-6);
            background: linear-gradient(135deg, var(--color-gray-50), var(--color-gray-100));
            border-radius: var(--radius-2xl);
            border: 1px solid var(--color-gray-200);
        }

        .winner-stats .stat-item {
            text-align: center;
        }

        .winner-stats .stat-value {
            display: block;
            font-size: 2rem;
            font-weight: 800;
            color: var(--color-gray-900);
            margin-bottom: var(--space-1);
        }

        .winner-stats .stat-label {
            font-size: 0.9rem;
            color: var(--color-gray-600);
            font-weight: 600;
        }

        /* Compact Rankings Card */
        .compact-rankings-card {
            grid-column: 2 / 3;
            grid-row: 1 / 2;
            background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
            color: white;
            border-radius: var(--radius-3xl);
            padding: var(--space-10);
            box-shadow: 0 25px 60px rgba(0, 0, 0, 0.3);
            transform: translateY(-40px);
        }

        .rankings-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--space-8);
            padding-bottom: var(--space-4);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .rankings-header h3 {
            font-size: 1.5rem;
            font-weight: 700;
        }

        .live-indicator {
            display: flex;
            align-items: center;
            gap: var(--space-2);
            font-size: 0.9rem;
            font-weight: 600;
        }

        .live-dot {
            width: 8px;
            height: 8px;
            background: #10B981;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        .rankings-list {
            display: flex;
            flex-direction: column;
            gap: var(--space-4);
        }

        .rank-item {
            display: flex;
            align-items: center;
            gap: var(--space-4);
            padding: var(--space-4);
            background: rgba(255, 255, 255, 0.05);
            border-radius: var(--radius-xl);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
        }

        .rank-item:hover {
            background: rgba(255, 255, 255, 0.1);
            transform: translateX(5px);
        }

        .rank-number {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            width: 35px;
            height: 35px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 800;
            font-size: 1.1rem;
        }

        .rank-info {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .rank-name {
            font-weight: 700;
            font-size: 1.1rem;
            margin-bottom: var(--space-1);
        }

        .rank-score {
            font-size: 0.9rem;
            opacity: 0.8;
        }

        .rank-trend {
            font-size: 0.85rem;
            font-weight: 600;
            padding: var(--space-1) var(--space-2);
            border-radius: var(--radius-md);
        }

        .rank-trend.up {
            background: rgba(16, 185, 129, 0.2);
            color: #10B981;
        }

        .rank-trend.down {
            background: rgba(239, 68, 68, 0.2);
            color: #EF4444;
        }

        /* Category Filters Card */
        .category-filters-card {
            grid-column: 2 / 3;
            grid-row: 2 / 3;
            background: linear-gradient(135deg, #6366F1 0%, #4F46E5 100%);
            color: white;
            border-radius: var(--radius-3xl);
            padding: var(--space-8);
            box-shadow: 0 20px 50px rgba(99, 102, 241, 0.3);
        }

        .category-filters-card h4 {
            font-size: 1.3rem;
            font-weight: 700;
            margin-bottom: var(--space-6);
        }

        .filter-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: var(--space-3);
        }

        .filter-chip {
            background: rgba(255, 255, 255, 0.15);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: white;
            padding: var(--space-3) var(--space-4);
            border-radius: var(--radius-xl);
            display: flex;
            align-items: center;
            gap: var(--space-2);
            font-weight: 600;
            font-size: 0.9rem;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .filter-chip:hover,
        .filter-chip.active {
            background: rgba(255, 255, 255, 0.25);
            transform: translateY(-2px);
        }

        /* CTA Card */
        .leaderboard-cta-card {
            position: absolute;
            bottom: -60px;
            right: 0;
            background: linear-gradient(135deg, #F59E0B 0%, #D97706 100%);
            color: white;
            border-radius: var(--radius-3xl);
            padding: var(--space-8);
            width: 280px;
            box-shadow: 0 25px 60px rgba(245, 158, 11, 0.4);
            z-index: 10;
        }

        .cta-content h4 {
            font-size: 1.4rem;
            font-weight: 700;
            margin-bottom: var(--space-2);
        }

        .cta-content p {
            opacity: 0.9;
            margin-bottom: var(--space-6);
        }

        .cta-button {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            padding: var(--space-4) var(--space-6);
            border-radius: var(--radius-xl);
            display: flex;
            align-items: center;
            gap: var(--space-3);
            text-decoration: none;
            font-weight: 700;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .cta-button:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        /* Responsive Design for Dynamic Leaderboard */
        @media (max-width: 1024px) {
            .leaderboard-dynamic-layout {
                grid-template-columns: 1fr;
                grid-template-rows: auto;
                gap: var(--space-12);
            }

            .winner-spotlight-card,
            .compact-rankings-card,
            .category-filters-card {
                grid-column: 1;
                grid-row: auto;
            }

            .compact-rankings-card {
                transform: translateY(0);
            }

            .leaderboard-cta-card {
                position: relative;
                bottom: auto;
                right: auto;
                width: 100%;
                margin-top: var(--space-8);
            }

            .filter-grid {
                grid-template-columns: repeat(4, 1fr);
            }
        }

        @media (max-width: 768px) {
            .winner-info {
                flex-direction: column;
                text-align: center;
                gap: var(--space-6);
            }

            .winner-avatar {
                width: 100px;
                height: 100px;
            }

            .winner-initial {
                font-size: 2.5rem;
            }

            .winner-name {
                font-size: 2rem;
            }

            .score-number {
                font-size: 2.5rem;
            }

            .winner-stats {
                flex-direction: column;
                gap: var(--space-4);
            }

            .filter-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        .leaderboard-container {
            background: var(--color-light);
            border-radius: var(--radius-2xl);
            padding: var(--space-8);
            color: var(--color-gray-900);
            box-shadow: var(--shadow-2xl);
        }

        .leaderboard-filters {
            display: flex;
            gap: var(--space-3);
            margin-bottom: var(--space-8);
            flex-wrap: wrap;
        }

        .filter-btn {
            padding: var(--space-3) var(--space-6);
            border: 1px solid var(--color-gray-300);
            background: white;
            color: var(--color-gray-600);
            border-radius: var(--radius-lg);
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .filter-btn.active,
        .filter-btn:hover {
            background: var(--color-primary);
            color: white;
            border-color: var(--color-primary);
        }

        .leaderboard-table {
            background: var(--color-gray-50);
            border-radius: var(--radius-xl);
            overflow: hidden;
        }

        .table-header {
            display: grid;
            grid-template-columns: 80px 1fr 120px 100px 100px;
            gap: var(--space-4);
            padding: var(--space-4) var(--space-6);
            background: var(--color-gray-100);
            font-weight: 600;
            font-size: 0.875rem;
            color: var(--color-gray-700);
        }

        .table-row {
            display: grid;
            grid-template-columns: 80px 1fr 120px 100px 100px;
            gap: var(--space-4);
            padding: var(--space-4) var(--space-6);
            border-bottom: 1px solid var(--color-gray-200);
            align-items: center;
            transition: background 0.2s ease;
        }

        .table-row:hover {
            background: white;
        }

        .table-row.winner {
            background: linear-gradient(135deg, rgba(245, 158, 11, 0.1), rgba(245, 158, 11, 0.05));
        }

        .rank-col {
            display: flex;
            align-items: center;
            gap: var(--space-2);
            font-weight: 600;
        }

        .rank-col i {
            color: var(--color-accent);
        }

        .ai-info {
            display: flex;
            flex-direction: column;
        }

        .ai-name {
            font-weight: 600;
            color: var(--color-gray-900);
        }

        .ai-provider {
            font-size: 0.875rem;
            color: var(--color-gray-500);
        }

        .score-col {
            display: flex;
            align-items: center;
            gap: var(--space-3);
        }

        .score-bar {
            width: 60px;
            height: 8px;
            background: var(--color-gray-200);
            border-radius: var(--radius-sm);
            overflow: hidden;
        }

        .score-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--color-primary), #059669);
            border-radius: var(--radius-sm);
        }

        .score-text {
            font-weight: 600;
            font-size: 0.875rem;
        }

        .votes-col {
            font-weight: 500;
            color: var(--color-gray-600);
        }

        .trend-col {
            display: flex;
            align-items: center;
            gap: var(--space-1);
            font-size: 0.875rem;
            font-weight: 500;
        }

        .trend-up {
            color: var(--color-primary);
        }

        .trend-down {
            color: #EF4444;
        }

        .leaderboard-cta {
            text-align: center;
            margin-top: var(--space-8);
        }

        /* Categories Showcase Section */
        .categories-showcase {
            padding: var(--space-24) 0;
            background: var(--color-dark-lighter);
            position: relative;
            overflow: hidden;
        }

        .categories-showcase::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at 20% 80%, rgba(245, 158, 11, 0.06) 0%, transparent 50%),
                        radial-gradient(circle at 80% 20%, rgba(16, 185, 129, 0.06) 0%, transparent 50%);
            pointer-events: none;
        }

        /* Dynamic Categories Layout */
        .categories-dynamic-layout {
            display: grid;
            grid-template-columns: 2fr 1fr;
            grid-template-rows: auto;
            gap: var(--space-12);
            position: relative;
            z-index: 1;
        }

        /* Featured Category Card */
        .category-featured {
            background: linear-gradient(135deg, var(--color-light) 0%, #f8fafc 100%);
            border-radius: var(--radius-3xl);
            padding: var(--space-16);
            position: relative;
            overflow: hidden;
            box-shadow: 0 30px 70px rgba(0, 0, 0, 0.12);
            border: 1px solid rgba(16, 185, 129, 0.1);
        }

        .category-featured::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 6px;
            background: linear-gradient(90deg, #3B82F6, #1D4ED8, #1E40AF);
        }

        .category-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--space-8);
        }

        .category-icon-large {
            background: linear-gradient(135deg, #3B82F6, #1D4ED8);
            color: white;
            width: 100px;
            height: 100px;
            border-radius: var(--radius-2xl);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.5rem;
            box-shadow: 0 15px 35px rgba(59, 130, 246, 0.3);
        }

        .category-badge {
            background: linear-gradient(135deg, #F59E0B, #D97706);
            color: white;
            padding: var(--space-3) var(--space-6);
            border-radius: var(--radius-2xl);
            font-weight: 700;
            font-size: 0.9rem;
            box-shadow: 0 8px 20px rgba(245, 158, 11, 0.3);
        }

        .category-title-large {
            font-size: 2.5rem;
            font-weight: 800;
            color: var(--color-gray-900);
            margin-bottom: var(--space-4);
            letter-spacing: -0.02em;
        }

        .category-description-large {
            font-size: 1.3rem;
            color: var(--color-gray-600);
            line-height: 1.6;
            margin-bottom: var(--space-10);
        }

        .category-metrics {
            display: flex;
            gap: var(--space-8);
            margin-bottom: var(--space-10);
            padding: var(--space-6);
            background: linear-gradient(135deg, var(--color-gray-50), var(--color-gray-100));
            border-radius: var(--radius-2xl);
            border: 1px solid var(--color-gray-200);
        }

        .metric-item {
            text-align: center;
        }

        .metric-value {
            display: block;
            font-size: 2rem;
            font-weight: 800;
            color: #3B82F6;
            margin-bottom: var(--space-1);
        }

        .metric-label {
            font-size: 0.9rem;
            color: var(--color-gray-600);
            font-weight: 600;
        }

        .category-tech-stack {
            display: flex;
            flex-wrap: wrap;
            gap: var(--space-3);
        }

        .tech-item {
            padding: var(--space-2) var(--space-4);
            border-radius: var(--radius-lg);
            font-weight: 600;
            font-size: 0.9rem;
            color: white;
        }

        .tech-item.python { background: linear-gradient(135deg, #3776AB, #2B5A87); }
        .tech-item.javascript { background: linear-gradient(135deg, #F7DF1E, #E6C200); color: #000; }
        .tech-item.java { background: linear-gradient(135deg, #ED8B00, #C7740A); }
        .tech-item.rust { background: linear-gradient(135deg, #CE422B, #A8341F); }

        .tech-more {
            padding: var(--space-2) var(--space-4);
            border-radius: var(--radius-lg);
            background: rgba(59, 130, 246, 0.1);
            color: #3B82F6;
            font-weight: 600;
            font-size: 0.9rem;
            border: 1px solid rgba(59, 130, 246, 0.2);
        }

        /* Compact Categories */
        .compact-categories {
            display: flex;
            flex-direction: column;
            gap: var(--space-6);
        }

        .category-compact {
            background: var(--color-light);
            border-radius: var(--radius-2xl);
            padding: var(--space-8);
            position: relative;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
            border-left: 4px solid transparent;
        }

        .category-knowledge {
            border-left-color: #8B5CF6;
        }

        .category-creative {
            border-left-color: #EC4899;
        }

        .category-support {
            border-left-color: #10B981;
        }

        .compact-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--space-4);
        }

        .category-icon-compact {
            width: 50px;
            height: 50px;
            border-radius: var(--radius-xl);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.3rem;
            color: white;
        }

        .category-knowledge .category-icon-compact {
            background: linear-gradient(135deg, #8B5CF6, #7C3AED);
        }

        .category-creative .category-icon-compact {
            background: linear-gradient(135deg, #EC4899, #DB2777);
        }

        .category-support .category-icon-compact {
            background: linear-gradient(135deg, #10B981, #059669);
        }

        .category-trend {
            display: flex;
            align-items: center;
            gap: var(--space-2);
            background: rgba(16, 185, 129, 0.1);
            color: var(--color-primary);
            padding: var(--space-2) var(--space-3);
            border-radius: var(--radius-lg);
            font-size: 0.85rem;
            font-weight: 600;
        }

        .category-title-compact {
            font-size: 1.3rem;
            font-weight: 700;
            color: var(--color-gray-900);
            margin-bottom: var(--space-3);
        }

        .category-stats-compact {
            display: flex;
            flex-direction: column;
            gap: var(--space-1);
            margin-bottom: var(--space-4);
        }

        .stat-primary {
            font-weight: 700;
            color: var(--color-gray-900);
            font-size: 1rem;
        }

        .stat-secondary {
            font-size: 0.85rem;
            color: var(--color-gray-600);
        }

        .category-preview {
            display: flex;
            gap: var(--space-2);
            flex-wrap: wrap;
        }

        .preview-item {
            background: var(--color-gray-100);
            color: var(--color-gray-700);
            padding: var(--space-1) var(--space-3);
            border-radius: var(--radius-md);
            font-size: 0.8rem;
            font-weight: 500;
        }

        .category-compact:hover {
            transform: translateY(-5px) scale(1.02);
            box-shadow: 0 20px 45px rgba(0, 0, 0, 0.12);
        }

        /* Floating Stats Card */
        .category-stats-floating {
            position: absolute;
            top: 50%;
            right: -80px;
            transform: translateY(-50%);
            background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
            color: white;
            border-radius: var(--radius-3xl);
            padding: var(--space-8);
            width: 280px;
            box-shadow: 0 25px 60px rgba(0, 0, 0, 0.4);
            z-index: 10;
        }

        .floating-stats-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--space-6);
        }

        .floating-stats-header h4 {
            font-size: 1.2rem;
            font-weight: 700;
        }

        .stats-period {
            background: rgba(255, 255, 255, 0.1);
            padding: var(--space-1) var(--space-3);
            border-radius: var(--radius-lg);
            font-size: 0.8rem;
            font-weight: 600;
        }

        .performance-chart {
            display: flex;
            align-items: end;
            gap: var(--space-4);
            height: 120px;
            margin-bottom: var(--space-4);
            padding: var(--space-4);
            background: rgba(255, 255, 255, 0.05);
            border-radius: var(--radius-xl);
        }

        .chart-bar {
            flex: 1;
            background: linear-gradient(to top, var(--color-primary), var(--color-secondary));
            border-radius: var(--radius-sm);
            position: relative;
            min-height: 20px;
            transition: all 0.3s ease;
        }

        .chart-bar:hover {
            transform: scaleY(1.1);
        }

        .bar-label {
            position: absolute;
            bottom: -25px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 0.7rem;
            font-weight: 600;
            white-space: nowrap;
        }

        .chart-legend {
            text-align: center;
            font-size: 0.9rem;
            opacity: 0.8;
        }

        .categories-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: var(--space-8);
        }

        .category-card {
            background: var(--color-light);
            border-radius: var(--radius-2xl);
            padding: var(--space-8);
            color: var(--color-gray-900);
            box-shadow: var(--shadow-lg);
            transition: transform 0.2s ease;
        }

        .category-card:hover {
            transform: translateY(-4px);
        }

        .category-icon {
            background: linear-gradient(135deg, var(--color-primary), #059669);
            color: white;
            width: 60px;
            height: 60px;
            border-radius: var(--radius-xl);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            margin-bottom: var(--space-6);
        }

        .category-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: var(--space-4);
            color: var(--color-gray-900);
        }

        .category-description {
            color: var(--color-gray-600);
            line-height: 1.6;
            margin-bottom: var(--space-6);
        }

        .category-stats {
            display: flex;
            gap: var(--space-6);
            margin-bottom: var(--space-6);
            padding: var(--space-4);
            background: var(--color-gray-50);
            border-radius: var(--radius-lg);
        }

        .stat {
            text-align: center;
        }

        .stat-number {
            display: block;
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--color-primary);
        }

        .stat-label {
            font-size: 0.875rem;
            color: var(--color-gray-500);
        }

        .category-languages {
            display: flex;
            flex-wrap: wrap;
            gap: var(--space-2);
        }

        .language-tag {
            background: rgba(16, 185, 129, 0.1);
            color: var(--color-primary);
            padding: var(--space-1) var(--space-3);
            border-radius: var(--radius-md);
            font-size: 0.875rem;
            font-weight: 500;
        }

        @media (max-width: 768px) {
            .table-header,
            .table-row {
                grid-template-columns: 60px 1fr 80px;
                gap: var(--space-2);
            }

            .votes-col,
            .trend-col {
                display: none;
            }

            .leaderboard-filters {
                justify-content: center;
            }

            .categories-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>

    <!-- Community Impact Section -->
    <section class="community-impact">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">Community Impact</h2>
                <p class="section-description">
                    Real people. Real impact. Real change.
                </p>
            </div>

            <div class="impact-dynamic-layout">
                <!-- Hero Stats Card -->
                <div class="hero-stats-card">
                    <div class="stats-header">
                        <h3>Platform Growth</h3>
                        <div class="growth-indicator">
                            <i class="fas fa-chart-line"></i>
                            <span>All-time high</span>
                        </div>
                    </div>
                    <div class="primary-stats">
                        <div class="primary-stat">
                            <span class="stat-number-large">8,234</span>
                            <span class="stat-label-large">Active Evaluators</span>
                            <div class="stat-trend-large">
                                <i class="fas fa-arrow-up"></i>
                                <span>+23% this month</span>
                            </div>
                        </div>
                        <div class="stats-grid">
                            <div class="mini-stat">
                                <span class="mini-number">156K</span>
                                <span class="mini-label">Responses</span>
                            </div>
                            <div class="mini-stat">
                                <span class="mini-number">12.8K</span>
                                <span class="mini-label">Topics</span>
                            </div>
                            <div class="mini-stat">
                                <span class="mini-number">47</span>
                                <span class="mini-label">AI Models</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Testimonials Carousel -->
                <div class="testimonials-carousel">
                    <div class="carousel-header">
                        <h4>What Our Community Says</h4>
                        <div class="carousel-controls">
                            <button class="carousel-btn prev">
                                <i class="fas fa-chevron-left"></i>
                            </button>
                            <button class="carousel-btn next">
                                <i class="fas fa-chevron-right"></i>
                            </button>
                        </div>
                    </div>
                    <div class="testimonial-active">
                        <div class="testimonial-quote">
                            "Huval changed how I evaluate AI models. Real performance data from actual users."
                        </div>
                        <div class="testimonial-author">
                            <div class="author-avatar-large">
                                <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjAiIGN5PSIyMCIgcj0iMjAiIGZpbGw9IiMxMEI5ODEiLz4KPHN2ZyB3aWR0aD0iMjAiIGhlaWdodD0iMjAiIHZpZXdCb3g9IjAgMCAyMCAyMCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEwIDEwQzEyLjIwOTEgMTAgMTQgOC4yMDkxNCAxNCA2QzE0IDMuNzkwODYgMTIuMjA5MSAyIDEwIDJDNy43OTA4NiAyIDYgMy43OTA4NiA2IDZDNiA4LjIwOTE0IDcuNzkwODYgMTAgMTAgMTBaIiBmaWxsPSJ3aGl0ZSIvPgo8cGF0aCBkPSJNMTAgMTJDNi42ODYyOSAxMiA0IDE0LjY4NjMgNCAxOFYyMEgxNlYxOEMxNiAxNC42ODYzIDEzLjMxMzcgMTIgMTAgMTJaIiBmaWxsPSJ3aGl0ZSIvPgo8L3N2Zz4KPC9zdmc+" alt="Sarah Chen" />
                            </div>
                            <div class="author-details">
                                <div class="author-name">Sarah Chen</div>
                                <div class="author-role">ML Engineer at TechCorp</div>
                                <div class="author-rating">
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Community Highlights -->
                <div class="community-highlights">
                    <div class="highlight-item highlight-featured">
                        <div class="highlight-icon">
                            <i class="fas fa-trophy"></i>
                        </div>
                        <div class="highlight-content">
                            <h5>Top Contributor</h5>
                            <p>Alex Kim evaluated 1,247 responses this month</p>
                        </div>
                    </div>

                    <div class="highlight-item">
                        <div class="highlight-icon">
                            <i class="fas fa-fire"></i>
                        </div>
                        <div class="highlight-content">
                            <h5>Trending Topic</h5>
                            <p>"Python vs Rust performance"</p>
                        </div>
                    </div>

                    <div class="highlight-item">
                        <div class="highlight-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="highlight-content">
                            <h5>New Members</h5>
                            <p>234 evaluators joined this week</p>
                        </div>
                    </div>
                </div>

                <!-- Floating Activity Feed -->
                <div class="activity-feed-floating">
                    <div class="feed-header">
                        <h4>Live Activity</h4>
                        <div class="live-pulse">
                            <div class="pulse-dot"></div>
                            <span>Live</span>
                        </div>
                    </div>
                    <div class="activity-list">
                        <div class="activity-item">
                            <div class="activity-avatar">M</div>
                            <div class="activity-text">
                                <span class="activity-user">Mike</span> voted on a coding topic
                            </div>
                            <div class="activity-time">2m ago</div>
                        </div>
                        <div class="activity-item">
                            <div class="activity-avatar">S</div>
                            <div class="activity-text">
                                <span class="activity-user">Sarah</span> created new topic
                            </div>
                            <div class="activity-time">5m ago</div>
                        </div>
                        <div class="activity-item">
                            <div class="activity-avatar">J</div>
                            <div class="activity-text">
                                <span class="activity-user">John</span> joined the community
                            </div>
                            <div class="activity-time">8m ago</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- For AI Providers Section -->
    <section class="ai-providers">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">For AI Providers</h2>
                <p class="section-description">
                    Real insights. Real improvement. Real results.
                </p>
            </div>

            <div class="providers-dynamic-layout">
                <!-- Main Provider Card -->
                <div class="provider-main-card">
                    <div class="provider-header">
                        <div class="provider-badge-large">
                            <i class="fas fa-robot"></i>
                            <span>AI Providers</span>
                        </div>
                        <div class="provider-status">
                            <span class="status-dot"></span>
                            <span>47 Models Connected</span>
                        </div>
                    </div>

                    <h3 class="provider-title-main">
                        Get Real Performance Insights
                        <span class="gradient-text">For Your AI Models</span>
                    </h3>

                    <p class="provider-description-main">
                        Connect your AI models and receive comprehensive analytics based on real user evaluations.
                    </p>

                    <div class="provider-features">
                        <div class="feature-item">
                            <div class="feature-icon">
                                <i class="fas fa-chart-line"></i>
                            </div>
                            <span>Performance Analytics</span>
                        </div>
                        <div class="feature-item">
                            <div class="feature-icon">
                                <i class="fas fa-users"></i>
                            </div>
                            <span>Real User Feedback</span>
                        </div>
                        <div class="feature-item">
                            <div class="feature-icon">
                                <i class="fas fa-target"></i>
                            </div>
                            <span>Improvement Tracking</span>
                        </div>
                    </div>

                    <div class="provider-cta-main">
                        <a href="register.html" class="btn btn-primary btn-large">
                            <i class="fas fa-plus"></i>
                            Add Your AI Model
                        </a>
                    </div>
                </div>

                <!-- Dashboard Preview -->
                <div class="dashboard-preview-card">
                    <div class="preview-header">
                        <h4>Performance Dashboard</h4>
                        <div class="preview-model">Your AI Model</div>
                    </div>

                    <div class="preview-metrics">
                        <div class="preview-metric primary">
                            <span class="metric-value-preview">87.3%</span>
                            <span class="metric-label-preview">Overall Score</span>
                            <div class="metric-trend-preview">
                                <i class="fas fa-arrow-up"></i>
                                <span>+3.2%</span>
                            </div>
                        </div>

                        <div class="preview-metric">
                            <span class="metric-value-preview">15,234</span>
                            <span class="metric-label-preview">Evaluations</span>
                        </div>
                    </div>

                    <div class="preview-categories">
                        <div class="preview-category">
                            <span class="category-label">Coding</span>
                            <div class="category-progress">
                                <div class="progress-fill" style="width: 92%"></div>
                            </div>
                            <span class="category-percentage">92%</span>
                        </div>
                        <div class="preview-category">
                            <span class="category-label">Knowledge</span>
                            <div class="category-progress">
                                <div class="progress-fill" style="width: 85%"></div>
                            </div>
                            <span class="category-percentage">85%</span>
                        </div>
                        <div class="preview-category">
                            <span class="category-label">Creative</span>
                            <div class="category-progress">
                                <div class="progress-fill" style="width: 78%"></div>
                            </div>
                            <span class="category-percentage">78%</span>
                        </div>
                    </div>
                </div>

                <!-- Benefits Cards -->
                <div class="benefits-cards">
                    <div class="benefit-card benefit-analytics">
                        <div class="benefit-icon-large">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <h5>Deep Analytics</h5>
                        <p>Comprehensive performance insights across all categories</p>
                    </div>

                    <div class="benefit-card benefit-feedback">
                        <div class="benefit-icon-large">
                            <i class="fas fa-users"></i>
                        </div>
                        <h5>User Feedback</h5>
                        <p>Real user perceptions and interaction data</p>
                    </div>

                    <div class="benefit-card benefit-tracking">
                        <div class="benefit-icon-large">
                            <i class="fas fa-target"></i>
                        </div>
                        <h5>Progress Tracking</h5>
                        <p>Monitor improvements and measure update impact</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Final CTA Section -->
    <section class="final-cta">
        <div class="container">
            <div class="cta-dynamic-layout">
                <!-- Main CTA Hero -->
                <div class="cta-hero-card">
                    <div class="cta-hero-content">
                        <h2 class="cta-title-hero">
                            Ready to Shape the Future of AI?
                        </h2>
                        <p class="cta-description-hero">
                            Join our community and help create the most comprehensive, unbiased AI evaluation platform.
                        </p>

                        <div class="cta-stats-mini">
                            <div class="mini-stat-item">
                                <span class="mini-stat-number">8,234</span>
                                <span class="mini-stat-text">Active Members</span>
                            </div>
                            <div class="mini-stat-item">
                                <span class="mini-stat-number">47</span>
                                <span class="mini-stat-text">AI Models</span>
                            </div>
                            <div class="mini-stat-item">
                                <span class="mini-stat-number">156K</span>
                                <span class="mini-stat-text">Evaluations</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- CTA Options -->
                <div class="cta-options-grid">
                    <div class="cta-option-card cta-evaluator">
                        <div class="option-header">
                            <div class="option-icon-large">
                                <i class="fas fa-user-plus"></i>
                            </div>
                            <div class="option-badge">Most Popular</div>
                        </div>
                        <h3 class="option-title">Join as Evaluator</h3>
                        <p class="option-description">
                            Start evaluating AI responses and contribute to the global leaderboard.
                        </p>
                        <div class="option-features">
                            <div class="feature-check">
                                <i class="fas fa-check"></i>
                                <span>Free forever</span>
                            </div>
                            <div class="feature-check">
                                <i class="fas fa-check"></i>
                                <span>Instant access</span>
                            </div>
                            <div class="feature-check">
                                <i class="fas fa-check"></i>
                                <span>Community rewards</span>
                            </div>
                        </div>
                        <a href="register.html" class="btn btn-primary btn-large">
                            <i class="fas fa-rocket"></i>
                            Get Started Free
                        </a>
                    </div>

                    <div class="cta-option-card cta-provider">
                        <div class="option-header">
                            <div class="option-icon-large">
                                <i class="fas fa-robot"></i>
                            </div>
                            <div class="option-badge provider">For Providers</div>
                        </div>
                        <h3 class="option-title">Add Your AI Model</h3>
                        <p class="option-description">
                            Connect your AI model and receive detailed performance insights.
                        </p>
                        <div class="option-features">
                            <div class="feature-check">
                                <i class="fas fa-check"></i>
                                <span>Performance analytics</span>
                            </div>
                            <div class="feature-check">
                                <i class="fas fa-check"></i>
                                <span>Real user feedback</span>
                            </div>
                            <div class="feature-check">
                                <i class="fas fa-check"></i>
                                <span>Improvement tracking</span>
                            </div>
                        </div>
                        <a href="register.html" class="btn btn-secondary btn-large">
                            <i class="fas fa-plus"></i>
                            Add Model
                        </a>
                    </div>
                </div>

                <!-- Trust Indicators -->
                <div class="trust-indicators-card">
                    <h4>Why Choose Huval?</h4>
                    <div class="trust-grid">
                        <div class="trust-item-large">
                            <div class="trust-icon">
                                <i class="fas fa-shield-alt"></i>
                            </div>
                            <div class="trust-content">
                                <h5>Secure & Private</h5>
                                <p>Your data is protected with enterprise-grade security</p>
                            </div>
                        </div>
                        <div class="trust-item-large">
                            <div class="trust-icon">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="trust-content">
                                <h5>Community Driven</h5>
                                <p>Powered by real users, not corporate interests</p>
                            </div>
                        </div>
                        <div class="trust-item-large">
                            <div class="trust-icon">
                                <i class="fas fa-chart-line"></i>
                            </div>
                            <div class="trust-content">
                                <h5>Transparent Results</h5>
                                <p>Open methodology and verifiable rankings</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-dynamic-layout">
                <!-- Footer Brand Section -->
                <div class="footer-brand-card">
                    <div class="footer-logo-section">
                        <a href="#" class="footer-logo">huval</a>
                        <div class="logo-tagline">AI Evaluation. Reimagined.</div>
                    </div>
                    <p class="footer-description">
                        Revolutionary AI evaluation platform powered by community-driven assessment.
                    </p>
                    <div class="footer-stats">
                        <div class="footer-stat">
                            <span class="stat-num">8K+</span>
                            <span class="stat-text">Evaluators</span>
                        </div>
                        <div class="footer-stat">
                            <span class="stat-num">47</span>
                            <span class="stat-text">AI Models</span>
                        </div>
                        <div class="footer-stat">
                            <span class="stat-num">156K</span>
                            <span class="stat-text">Evaluations</span>
                        </div>
                    </div>
                    <div class="social-links-enhanced">
                        <a href="#" class="social-link twitter">
                            <i class="fab fa-twitter"></i>
                        </a>
                        <a href="#" class="social-link github">
                            <i class="fab fa-github"></i>
                        </a>
                        <a href="#" class="social-link discord">
                            <i class="fab fa-discord"></i>
                        </a>
                        <a href="#" class="social-link linkedin">
                            <i class="fab fa-linkedin"></i>
                        </a>
                    </div>
                </div>

                <!-- Footer Links Grid -->
                <div class="footer-links-grid">
                    <div class="link-group-enhanced platform">
                        <h4>Platform</h4>
                        <div class="links-list">
                            <a href="leaderboard.html">
                                <i class="fas fa-trophy"></i>
                                <span>Leaderboard</span>
                            </a>
                            <a href="topics.html">
                                <i class="fas fa-list"></i>
                                <span>Browse Topics</span>
                            </a>
                            <a href="create-topic.html">
                                <i class="fas fa-plus"></i>
                                <span>Create Topic</span>
                            </a>
                            <a href="#categories">
                                <i class="fas fa-tags"></i>
                                <span>Categories</span>
                            </a>
                        </div>
                    </div>

                    <div class="link-group-enhanced community">
                        <h4>Community</h4>
                        <div class="links-list">
                            <a href="#community">
                                <i class="fas fa-users"></i>
                                <span>Join Community</span>
                            </a>
                            <a href="#">
                                <i class="fas fa-book"></i>
                                <span>Guidelines</span>
                            </a>
                            <a href="#">
                                <i class="fas fa-question-circle"></i>
                                <span>Help Center</span>
                            </a>
                            <a href="#">
                                <i class="fas fa-envelope"></i>
                                <span>Contact</span>
                            </a>
                        </div>
                    </div>

                    <div class="link-group-enhanced developers">
                        <h4>For Developers</h4>
                        <div class="links-list">
                            <a href="#">
                                <i class="fas fa-code"></i>
                                <span>API Documentation</span>
                            </a>
                            <a href="#">
                                <i class="fas fa-robot"></i>
                                <span>Add AI Model</span>
                            </a>
                            <a href="#">
                                <i class="fas fa-cogs"></i>
                                <span>Integration Guide</span>
                            </a>
                            <a href="#">
                                <i class="fas fa-heartbeat"></i>
                                <span>Status</span>
                            </a>
                        </div>
                    </div>

                    <div class="link-group-enhanced legal">
                        <h4>Legal</h4>
                        <div class="links-list">
                            <a href="#">
                                <i class="fas fa-shield-alt"></i>
                                <span>Privacy Policy</span>
                            </a>
                            <a href="#">
                                <i class="fas fa-file-contract"></i>
                                <span>Terms of Service</span>
                            </a>
                            <a href="#">
                                <i class="fas fa-cookie-bite"></i>
                                <span>Cookie Policy</span>
                            </a>
                            <a href="#">
                                <i class="fas fa-balance-scale"></i>
                                <span>GDPR</span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Footer Bottom -->
            <div class="footer-bottom-enhanced">
                <div class="footer-bottom-left">
                    <p>&copy; 2025 Huval. All rights reserved.</p>
                    <p>Building the future of AI evaluation through community collaboration.</p>
                </div>
                <div class="footer-bottom-right">
                    <div class="footer-badge">
                        <i class="fas fa-heart"></i>
                        <span>Made with love for the AI community</span>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <style>
        /* Community Impact Section */
        .community-impact {
            padding: var(--space-24) 0;
            background: var(--color-dark);
            position: relative;
            overflow: hidden;
        }

        .community-impact::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at 40% 60%, rgba(99, 102, 241, 0.08) 0%, transparent 50%),
                        radial-gradient(circle at 60% 40%, rgba(236, 72, 153, 0.08) 0%, transparent 50%);
            pointer-events: none;
        }

        /* Dynamic Impact Layout */
        .impact-dynamic-layout {
            display: grid;
            grid-template-columns: 1.5fr 1fr;
            grid-template-rows: auto auto;
            gap: var(--space-10);
            position: relative;
            z-index: 1;
        }

        /* Hero Stats Card */
        .hero-stats-card {
            grid-column: 1 / 2;
            grid-row: 1 / 3;
            background: linear-gradient(135deg, var(--color-light) 0%, #f8fafc 100%);
            border-radius: var(--radius-3xl);
            padding: var(--space-16);
            position: relative;
            overflow: hidden;
            box-shadow: 0 30px 70px rgba(0, 0, 0, 0.15);
        }

        .hero-stats-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 6px;
            background: linear-gradient(90deg, #6366F1, #8B5CF6, #EC4899);
        }

        .stats-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--space-10);
        }

        .stats-header h3 {
            font-size: 2rem;
            font-weight: 800;
            color: var(--color-gray-900);
        }

        .growth-indicator {
            display: flex;
            align-items: center;
            gap: var(--space-2);
            background: rgba(16, 185, 129, 0.1);
            color: var(--color-primary);
            padding: var(--space-3) var(--space-5);
            border-radius: var(--radius-2xl);
            font-weight: 700;
            border: 1px solid rgba(16, 185, 129, 0.2);
        }

        .primary-stats {
            display: flex;
            flex-direction: column;
            gap: var(--space-8);
        }

        .primary-stat {
            text-align: center;
            padding: var(--space-8);
            background: linear-gradient(135deg, var(--color-gray-50), var(--color-gray-100));
            border-radius: var(--radius-2xl);
            border: 1px solid var(--color-gray-200);
        }

        .stat-number-large {
            display: block;
            font-size: 4rem;
            font-weight: 900;
            color: #6366F1;
            margin-bottom: var(--space-2);
            background: linear-gradient(135deg, #6366F1, #8B5CF6);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .stat-label-large {
            display: block;
            font-size: 1.3rem;
            color: var(--color-gray-700);
            font-weight: 700;
            margin-bottom: var(--space-4);
        }

        .stat-trend-large {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: var(--space-2);
            background: rgba(16, 185, 129, 0.1);
            color: var(--color-primary);
            padding: var(--space-2) var(--space-4);
            border-radius: var(--radius-lg);
            font-weight: 700;
            display: inline-flex;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: var(--space-4);
        }

        .mini-stat {
            text-align: center;
            padding: var(--space-4);
            background: white;
            border-radius: var(--radius-xl);
            border: 1px solid var(--color-gray-200);
        }

        .mini-number {
            display: block;
            font-size: 1.5rem;
            font-weight: 800;
            color: var(--color-gray-900);
            margin-bottom: var(--space-1);
        }

        .mini-label {
            font-size: 0.8rem;
            color: var(--color-gray-600);
            font-weight: 600;
        }

        /* Testimonials Carousel */
        .testimonials-carousel {
            grid-column: 2 / 3;
            grid-row: 1 / 2;
            background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
            color: white;
            border-radius: var(--radius-3xl);
            padding: var(--space-10);
            box-shadow: 0 25px 60px rgba(0, 0, 0, 0.3);
            transform: translateY(-40px);
        }

        .carousel-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--space-8);
        }

        .carousel-header h4 {
            font-size: 1.3rem;
            font-weight: 700;
        }

        .carousel-controls {
            display: flex;
            gap: var(--space-2);
        }

        .carousel-btn {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: white;
            width: 35px;
            height: 35px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .carousel-btn:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        .testimonial-quote {
            font-size: 1.1rem;
            line-height: 1.6;
            font-style: italic;
            margin-bottom: var(--space-6);
            opacity: 0.95;
        }

        .testimonial-author {
            display: flex;
            align-items: center;
            gap: var(--space-4);
        }

        .author-avatar-large {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            overflow: hidden;
            border: 3px solid rgba(255, 255, 255, 0.2);
        }

        .author-avatar-large img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .author-name {
            font-weight: 700;
            font-size: 1.1rem;
            margin-bottom: var(--space-1);
        }

        .author-role {
            font-size: 0.9rem;
            opacity: 0.8;
            margin-bottom: var(--space-2);
        }

        .author-rating {
            display: flex;
            gap: var(--space-1);
            color: #F59E0B;
        }

        /* Community Highlights */
        .community-highlights {
            grid-column: 2 / 3;
            grid-row: 2 / 3;
            display: flex;
            flex-direction: column;
            gap: var(--space-4);
        }

        .highlight-item {
            background: var(--color-light);
            border-radius: var(--radius-2xl);
            padding: var(--space-6);
            display: flex;
            align-items: center;
            gap: var(--space-4);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
            border-left: 4px solid transparent;
        }

        .highlight-featured {
            border-left-color: #F59E0B;
            background: linear-gradient(135deg, #FEF3C7, #FDE68A);
        }

        .highlight-item:hover {
            transform: translateX(5px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.12);
        }

        .highlight-icon {
            width: 50px;
            height: 50px;
            border-radius: var(--radius-xl);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
            color: white;
        }

        .highlight-featured .highlight-icon {
            background: linear-gradient(135deg, #F59E0B, #D97706);
        }

        .highlight-content h5 {
            font-size: 1.1rem;
            font-weight: 700;
            color: var(--color-gray-900);
            margin-bottom: var(--space-1);
        }

        .highlight-content p {
            font-size: 0.9rem;
            color: var(--color-gray-600);
        }

        /* Floating Activity Feed */
        .activity-feed-floating {
            position: absolute;
            bottom: -60px;
            right: 0;
            background: linear-gradient(135deg, #EC4899 0%, #DB2777 100%);
            color: white;
            border-radius: var(--radius-3xl);
            padding: var(--space-8);
            width: 320px;
            box-shadow: 0 25px 60px rgba(236, 72, 153, 0.4);
            z-index: 10;
        }

        .feed-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--space-6);
        }

        .feed-header h4 {
            font-size: 1.2rem;
            font-weight: 700;
        }

        .live-pulse {
            display: flex;
            align-items: center;
            gap: var(--space-2);
            font-size: 0.9rem;
            font-weight: 600;
        }

        .pulse-dot {
            width: 8px;
            height: 8px;
            background: #10B981;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        .activity-list {
            display: flex;
            flex-direction: column;
            gap: var(--space-4);
        }

        .activity-item {
            display: flex;
            align-items: center;
            gap: var(--space-3);
            padding: var(--space-3);
            background: rgba(255, 255, 255, 0.1);
            border-radius: var(--radius-xl);
            backdrop-filter: blur(10px);
        }

        .activity-avatar {
            width: 35px;
            height: 35px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 700;
            font-size: 0.9rem;
        }

        .activity-text {
            flex: 1;
            font-size: 0.9rem;
        }

        .activity-user {
            font-weight: 700;
        }

        .activity-time {
            font-size: 0.8rem;
            opacity: 0.8;
        }

        /* AI Providers Section */
        .ai-providers {
            padding: var(--space-24) 0;
            background: var(--color-dark-lighter);
            position: relative;
            overflow: hidden;
        }

        .ai-providers::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at 80% 20%, rgba(59, 130, 246, 0.08) 0%, transparent 50%),
                        radial-gradient(circle at 20% 80%, rgba(139, 92, 246, 0.08) 0%, transparent 50%);
            pointer-events: none;
        }

        /* Providers Dynamic Layout */
        .providers-dynamic-layout {
            display: grid;
            grid-template-columns: 1.5fr 1fr;
            grid-template-rows: auto auto;
            gap: var(--space-10);
            position: relative;
            z-index: 1;
        }

        /* Provider Main Card */
        .provider-main-card {
            grid-column: 1 / 2;
            grid-row: 1 / 3;
            background: linear-gradient(135deg, var(--color-light) 0%, #f8fafc 100%);
            border-radius: var(--radius-3xl);
            padding: var(--space-16);
            position: relative;
            overflow: hidden;
            box-shadow: 0 30px 70px rgba(0, 0, 0, 0.12);
        }

        .provider-main-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 6px;
            background: linear-gradient(90deg, #3B82F6, #6366F1, #8B5CF6);
        }

        .provider-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--space-8);
        }

        .provider-badge-large {
            display: flex;
            align-items: center;
            gap: var(--space-3);
            background: linear-gradient(135deg, #3B82F6, #6366F1);
            color: white;
            padding: var(--space-4) var(--space-6);
            border-radius: var(--radius-2xl);
            font-weight: 700;
            font-size: 1.1rem;
            box-shadow: 0 10px 25px rgba(59, 130, 246, 0.3);
        }

        .provider-status {
            display: flex;
            align-items: center;
            gap: var(--space-2);
            background: rgba(16, 185, 129, 0.1);
            color: var(--color-primary);
            padding: var(--space-3) var(--space-5);
            border-radius: var(--radius-xl);
            font-weight: 600;
            border: 1px solid rgba(16, 185, 129, 0.2);
        }

        .status-dot {
            width: 8px;
            height: 8px;
            background: var(--color-primary);
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        .provider-title-main {
            font-size: 2.5rem;
            font-weight: 800;
            color: var(--color-gray-900);
            margin-bottom: var(--space-6);
            letter-spacing: -0.02em;
        }

        .provider-description-main {
            font-size: 1.3rem;
            color: var(--color-gray-600);
            line-height: 1.6;
            margin-bottom: var(--space-10);
        }

        .provider-features {
            display: flex;
            flex-direction: column;
            gap: var(--space-4);
            margin-bottom: var(--space-10);
        }

        .feature-item {
            display: flex;
            align-items: center;
            gap: var(--space-4);
            padding: var(--space-4);
            background: linear-gradient(135deg, var(--color-gray-50), var(--color-gray-100));
            border-radius: var(--radius-xl);
            border: 1px solid var(--color-gray-200);
        }

        .feature-icon {
            background: linear-gradient(135deg, #3B82F6, #6366F1);
            color: white;
            width: 50px;
            height: 50px;
            border-radius: var(--radius-xl);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
        }

        .feature-item span {
            font-weight: 600;
            color: var(--color-gray-900);
            font-size: 1.1rem;
        }

        /* Dashboard Preview Card */
        .dashboard-preview-card {
            grid-column: 2 / 3;
            grid-row: 1 / 2;
            background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
            color: white;
            border-radius: var(--radius-3xl);
            padding: var(--space-10);
            box-shadow: 0 25px 60px rgba(0, 0, 0, 0.3);
            transform: translateY(-40px);
        }

        .preview-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--space-8);
            padding-bottom: var(--space-4);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .preview-header h4 {
            font-size: 1.3rem;
            font-weight: 700;
        }

        .preview-model {
            background: rgba(59, 130, 246, 0.2);
            color: #60A5FA;
            padding: var(--space-2) var(--space-4);
            border-radius: var(--radius-lg);
            font-size: 0.9rem;
            font-weight: 600;
        }

        .preview-metrics {
            display: flex;
            gap: var(--space-6);
            margin-bottom: var(--space-8);
        }

        .preview-metric {
            text-align: center;
            padding: var(--space-4);
            background: rgba(255, 255, 255, 0.05);
            border-radius: var(--radius-xl);
            flex: 1;
        }

        .preview-metric.primary {
            background: rgba(59, 130, 246, 0.1);
            border: 1px solid rgba(59, 130, 246, 0.2);
        }

        .metric-value-preview {
            display: block;
            font-size: 1.8rem;
            font-weight: 800;
            margin-bottom: var(--space-1);
        }

        .metric-label-preview {
            font-size: 0.8rem;
            opacity: 0.8;
        }

        .metric-trend-preview {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: var(--space-1);
            margin-top: var(--space-2);
            color: var(--color-primary);
            font-size: 0.8rem;
            font-weight: 600;
        }

        .preview-categories {
            display: flex;
            flex-direction: column;
            gap: var(--space-4);
        }

        .preview-category {
            display: flex;
            align-items: center;
            gap: var(--space-3);
        }

        .category-label {
            font-size: 0.9rem;
            font-weight: 600;
            min-width: 80px;
        }

        .category-progress {
            flex: 1;
            height: 8px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: var(--radius-sm);
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--color-primary), var(--color-secondary));
            border-radius: var(--radius-sm);
        }

        .category-percentage {
            font-size: 0.8rem;
            font-weight: 600;
            min-width: 35px;
            text-align: right;
        }

        /* Benefits Cards */
        .benefits-cards {
            grid-column: 2 / 3;
            grid-row: 2 / 3;
            display: flex;
            flex-direction: column;
            gap: var(--space-4);
        }

        .benefit-card {
            background: var(--color-light);
            border-radius: var(--radius-2xl);
            padding: var(--space-6);
            display: flex;
            align-items: center;
            gap: var(--space-4);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
            border-left: 4px solid transparent;
        }

        .benefit-analytics { border-left-color: #3B82F6; }
        .benefit-feedback { border-left-color: #10B981; }
        .benefit-tracking { border-left-color: #8B5CF6; }

        .benefit-card:hover {
            transform: translateX(5px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.12);
        }

        .benefit-icon-large {
            width: 50px;
            height: 50px;
            border-radius: var(--radius-xl);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            color: white;
        }

        .benefit-analytics .benefit-icon-large {
            background: linear-gradient(135deg, #3B82F6, #1D4ED8);
        }

        .benefit-feedback .benefit-icon-large {
            background: linear-gradient(135deg, #10B981, #059669);
        }

        .benefit-tracking .benefit-icon-large {
            background: linear-gradient(135deg, #8B5CF6, #7C3AED);
        }

        .benefit-card h5 {
            font-size: 1.1rem;
            font-weight: 700;
            color: var(--color-gray-900);
            margin-bottom: var(--space-1);
        }

        .benefit-card p {
            font-size: 0.9rem;
            color: var(--color-gray-600);
        }

        /* Final CTA Section */
        .final-cta {
            padding: var(--space-24) 0;
            background: linear-gradient(135deg, var(--color-dark) 0%, var(--color-dark-lighter) 100%);
            position: relative;
            overflow: hidden;
        }

        .final-cta::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at 30% 70%, rgba(16, 185, 129, 0.1) 0%, transparent 50%),
                        radial-gradient(circle at 70% 30%, rgba(99, 102, 241, 0.1) 0%, transparent 50%);
            pointer-events: none;
        }

        /* CTA Dynamic Layout */
        .cta-dynamic-layout {
            display: grid;
            grid-template-columns: 1fr;
            gap: var(--space-16);
            position: relative;
            z-index: 1;
        }

        /* CTA Hero Card */
        .cta-hero-card {
            background: linear-gradient(135deg, var(--color-light) 0%, #f8fafc 100%);
            border-radius: var(--radius-3xl);
            padding: var(--space-16);
            text-align: center;
            position: relative;
            overflow: hidden;
            box-shadow: 0 30px 70px rgba(0, 0, 0, 0.15);
        }

        .cta-hero-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 6px;
            background: linear-gradient(90deg, var(--color-primary), var(--color-secondary), var(--color-accent));
        }

        .cta-title-hero {
            font-size: 3.5rem;
            font-weight: 900;
            color: var(--color-gray-900);
            margin-bottom: var(--space-6);
            letter-spacing: -0.03em;
        }

        .cta-description-hero {
            font-size: 1.4rem;
            color: var(--color-gray-600);
            line-height: 1.6;
            max-width: 700px;
            margin: 0 auto var(--space-10);
        }

        .cta-stats-mini {
            display: flex;
            justify-content: center;
            gap: var(--space-12);
            margin-bottom: var(--space-8);
        }

        .mini-stat-item {
            text-align: center;
        }

        .mini-stat-number {
            display: block;
            font-size: 2.5rem;
            font-weight: 900;
            color: var(--color-primary);
            margin-bottom: var(--space-1);
        }

        .mini-stat-text {
            font-size: 1rem;
            color: var(--color-gray-600);
            font-weight: 600;
        }

        /* CTA Options Grid */
        .cta-options-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: var(--space-10);
        }

        .cta-option-card {
            background: var(--color-light);
            border-radius: var(--radius-3xl);
            padding: var(--space-12);
            position: relative;
            overflow: hidden;
            box-shadow: 0 20px 50px rgba(0, 0, 0, 0.1);
            transition: all 0.4s ease;
            border: 2px solid transparent;
        }

        .cta-evaluator {
            border-color: rgba(16, 185, 129, 0.2);
        }

        .cta-provider {
            border-color: rgba(99, 102, 241, 0.2);
        }

        .cta-option-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 30px 70px rgba(0, 0, 0, 0.15);
        }

        .option-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--space-8);
        }

        .option-icon-large {
            background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
            color: white;
            width: 80px;
            height: 80px;
            border-radius: var(--radius-2xl);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            box-shadow: 0 15px 35px rgba(16, 185, 129, 0.3);
        }

        .cta-provider .option-icon-large {
            background: linear-gradient(135deg, #6366F1, #8B5CF6);
            box-shadow: 0 15px 35px rgba(99, 102, 241, 0.3);
        }

        .option-badge {
            background: linear-gradient(135deg, #F59E0B, #D97706);
            color: white;
            padding: var(--space-2) var(--space-4);
            border-radius: var(--radius-xl);
            font-size: 0.8rem;
            font-weight: 700;
        }

        .option-badge.provider {
            background: linear-gradient(135deg, #8B5CF6, #7C3AED);
        }

        .option-title {
            font-size: 2rem;
            font-weight: 800;
            color: var(--color-gray-900);
            margin-bottom: var(--space-4);
            letter-spacing: -0.02em;
        }

        .option-description {
            font-size: 1.1rem;
            color: var(--color-gray-600);
            line-height: 1.6;
            margin-bottom: var(--space-8);
        }

        .option-features {
            display: flex;
            flex-direction: column;
            gap: var(--space-3);
            margin-bottom: var(--space-10);
        }

        .feature-check {
            display: flex;
            align-items: center;
            gap: var(--space-3);
        }

        .feature-check i {
            background: var(--color-primary);
            color: white;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.7rem;
        }

        .feature-check span {
            font-weight: 600;
            color: var(--color-gray-700);
        }

        /* Trust Indicators Card */
        .trust-indicators-card {
            background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
            color: white;
            border-radius: var(--radius-3xl);
            padding: var(--space-12);
            box-shadow: 0 25px 60px rgba(0, 0, 0, 0.3);
        }

        .trust-indicators-card h4 {
            font-size: 2rem;
            font-weight: 800;
            text-align: center;
            margin-bottom: var(--space-10);
        }

        .trust-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: var(--space-8);
        }

        .trust-item-large {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
            padding: var(--space-6);
            background: rgba(255, 255, 255, 0.05);
            border-radius: var(--radius-2xl);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .trust-icon {
            background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
            color: white;
            width: 60px;
            height: 60px;
            border-radius: var(--radius-xl);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            margin-bottom: var(--space-4);
        }

        .trust-content h5 {
            font-size: 1.3rem;
            font-weight: 700;
            margin-bottom: var(--space-2);
        }

        .trust-content p {
            font-size: 0.95rem;
            opacity: 0.9;
            line-height: 1.5;
        }

        /* Enhanced Footer */
        .footer {
            background: linear-gradient(135deg, #0F172A 0%, #1E293B 100%);
            padding: var(--space-20) 0 var(--space-10);
            border-top: 1px solid rgba(16, 185, 129, 0.1);
            position: relative;
            overflow: hidden;
        }

        .footer::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at 20% 80%, rgba(16, 185, 129, 0.05) 0%, transparent 50%),
                        radial-gradient(circle at 80% 20%, rgba(99, 102, 241, 0.05) 0%, transparent 50%);
            pointer-events: none;
        }

        /* Footer Dynamic Layout */
        .footer-dynamic-layout {
            display: grid;
            grid-template-columns: 1.5fr 2fr;
            gap: var(--space-16);
            margin-bottom: var(--space-16);
            position: relative;
            z-index: 1;
        }

        /* Footer Brand Card */
        .footer-brand-card {
            background: rgba(255, 255, 255, 0.05);
            border-radius: var(--radius-3xl);
            padding: var(--space-10);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .footer-logo-section {
            margin-bottom: var(--space-6);
        }

        .footer-logo {
            font-size: 2.5rem;
            font-weight: 900;
            background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-decoration: none;
            display: block;
            margin-bottom: var(--space-2);
        }

        .logo-tagline {
            font-size: 1rem;
            color: var(--color-gray-400);
            font-weight: 600;
            font-style: italic;
        }

        .footer-description {
            color: var(--color-gray-300);
            line-height: 1.6;
            margin-bottom: var(--space-8);
            font-size: 1.1rem;
        }

        .footer-stats {
            display: flex;
            gap: var(--space-6);
            margin-bottom: var(--space-8);
            padding: var(--space-4);
            background: rgba(255, 255, 255, 0.05);
            border-radius: var(--radius-xl);
        }

        .footer-stat {
            text-align: center;
        }

        .stat-num {
            display: block;
            font-size: 1.5rem;
            font-weight: 800;
            color: var(--color-primary);
            margin-bottom: var(--space-1);
        }

        .stat-text {
            font-size: 0.8rem;
            color: var(--color-gray-400);
            font-weight: 600;
        }

        .social-links-enhanced {
            display: flex;
            gap: var(--space-4);
        }

        .social-link {
            background: rgba(255, 255, 255, 0.1);
            color: var(--color-gray-400);
            width: 50px;
            height: 50px;
            border-radius: var(--radius-xl);
            display: flex;
            align-items: center;
            justify-content: center;
            text-decoration: none;
            transition: all 0.3s ease;
            font-size: 1.2rem;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .social-link:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
        }

        .social-link.twitter:hover {
            background: #1DA1F2;
            color: white;
        }

        .social-link.github:hover {
            background: #333;
            color: white;
        }

        .social-link.discord:hover {
            background: #5865F2;
            color: white;
        }

        .social-link.linkedin:hover {
            background: #0077B5;
            color: white;
        }

        /* Footer Links Grid */
        .footer-links-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: var(--space-8);
        }

        .link-group-enhanced {
            background: rgba(255, 255, 255, 0.03);
            border-radius: var(--radius-2xl);
            padding: var(--space-8);
            border: 1px solid rgba(255, 255, 255, 0.05);
        }

        .link-group-enhanced h4 {
            font-size: 1.2rem;
            font-weight: 700;
            color: var(--color-light);
            margin-bottom: var(--space-6);
            display: flex;
            align-items: center;
            gap: var(--space-2);
        }

        .platform h4::before { content: '🏆'; }
        .community h4::before { content: '👥'; }
        .developers h4::before { content: '⚡'; }
        .legal h4::before { content: '📋'; }

        .links-list {
            display: flex;
            flex-direction: column;
            gap: var(--space-3);
        }

        .links-list a {
            display: flex;
            align-items: center;
            gap: var(--space-3);
            color: var(--color-gray-400);
            text-decoration: none;
            padding: var(--space-2) var(--space-3);
            border-radius: var(--radius-lg);
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .links-list a:hover {
            color: var(--color-primary);
            background: rgba(16, 185, 129, 0.1);
            transform: translateX(5px);
        }

        .links-list a i {
            width: 16px;
            text-align: center;
            opacity: 0.7;
        }

        /* Footer Bottom Enhanced */
        .footer-bottom-enhanced {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-top: var(--space-8);
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            position: relative;
            z-index: 1;
        }

        .footer-bottom-left p {
            color: var(--color-gray-500);
            margin-bottom: var(--space-1);
        }

        .footer-bottom-left p:first-child {
            font-weight: 600;
        }

        .footer-badge {
            display: flex;
            align-items: center;
            gap: var(--space-2);
            background: rgba(16, 185, 129, 0.1);
            color: var(--color-primary);
            padding: var(--space-3) var(--space-5);
            border-radius: var(--radius-xl);
            font-weight: 600;
            border: 1px solid rgba(16, 185, 129, 0.2);
        }

        .footer-badge i {
            animation: heartbeat 2s ease-in-out infinite;
        }

        @keyframes heartbeat {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        /* Comprehensive Responsive Design */
        @media (max-width: 1024px) {
            .categories-dynamic-layout,
            .impact-dynamic-layout,
            .providers-dynamic-layout {
                grid-template-columns: 1fr;
                gap: var(--space-12);
            }

            .category-stats-floating,
            .activity-feed-floating,
            .leaderboard-cta-card {
                position: relative;
                top: auto;
                right: auto;
                transform: none;
                width: 100%;
                margin-top: var(--space-8);
            }

            .compact-rankings-card,
            .dashboard-preview-card {
                transform: translateY(0);
            }

            .footer-dynamic-layout {
                grid-template-columns: 1fr;
                gap: var(--space-12);
            }

            .footer-links-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: var(--space-6);
            }

            .cta-options-grid {
                grid-template-columns: 1fr;
                gap: var(--space-8);
            }

            .trust-grid {
                grid-template-columns: 1fr;
                gap: var(--space-6);
            }
        }

        @media (max-width: 768px) {
            .hero-title {
                font-size: 3rem;
            }

            .section-title {
                font-size: 2.5rem;
            }

            .cta-title-hero,
            .provider-title-main,
            .category-title-large {
                font-size: 2rem;
            }

            .hero-stats {
                grid-template-columns: repeat(2, 1fr);
                gap: var(--space-6);
            }

            .cta-stats-mini {
                flex-direction: column;
                gap: var(--space-6);
            }

            .footer-bottom-enhanced {
                flex-direction: column;
                gap: var(--space-4);
                text-align: center;
            }

            .nav-links {
                display: none;
            }

            .mobile-menu-btn {
                display: block;
            }

            .filter-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .performance-chart {
                height: 80px;
            }
        }

        @media (max-width: 480px) {
            .hero-title {
                font-size: 2.5rem;
            }

            .section-title {
                font-size: 2rem;
            }

            .hero-stats {
                grid-template-columns: 1fr;
            }

            .footer-links-grid {
                grid-template-columns: 1fr;
            }

            .btn-large {
                padding: var(--space-4) var(--space-6);
                font-size: 1rem;
            }
        }

        .impact-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--space-16);
            align-items: start;
        }

        .impact-stats {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: var(--space-6);
        }

        .stat-card {
            background: var(--color-light);
            border-radius: var(--radius-2xl);
            padding: var(--space-6);
            color: var(--color-gray-900);
            box-shadow: var(--shadow-lg);
            display: flex;
            align-items: center;
            gap: var(--space-4);
        }

        .stat-icon {
            background: linear-gradient(135deg, var(--color-primary), #059669);
            color: white;
            width: 50px;
            height: 50px;
            border-radius: var(--radius-xl);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.25rem;
        }

        .stat-content {
            flex: 1;
        }

        .stat-number {
            font-size: 1.75rem;
            font-weight: 700;
            color: var(--color-gray-900);
            margin-bottom: var(--space-1);
        }

        .stat-label {
            font-size: 0.875rem;
            color: var(--color-gray-600);
            margin-bottom: var(--space-1);
        }

        .stat-growth {
            font-size: 0.875rem;
            color: var(--color-primary);
            font-weight: 500;
        }

        .testimonials {
            display: flex;
            flex-direction: column;
            gap: var(--space-6);
        }

        .testimonial-card {
            background: var(--color-light);
            border-radius: var(--radius-2xl);
            padding: var(--space-8);
            color: var(--color-gray-900);
            box-shadow: var(--shadow-lg);
        }

        .testimonial-content {
            font-size: 1.1rem;
            line-height: 1.6;
            color: var(--color-gray-700);
            margin-bottom: var(--space-6);
            font-style: italic;
        }

        .testimonial-author {
            display: flex;
            align-items: center;
            gap: var(--space-4);
        }

        .author-avatar {
            background: var(--color-primary);
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .author-name {
            font-weight: 600;
            color: var(--color-gray-900);
        }

        .author-role {
            font-size: 0.875rem;
            color: var(--color-gray-500);
        }

        /* AI Providers Section */
        .ai-providers {
            padding: var(--space-24) 0;
            background: var(--color-dark-lighter);
        }

        .provider-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--space-16);
            align-items: center;
        }

        .provider-badge {
            display: inline-flex;
            align-items: center;
            gap: var(--space-2);
            background: rgba(99, 102, 241, 0.1);
            border: 1px solid rgba(99, 102, 241, 0.2);
            color: var(--color-secondary);
            padding: var(--space-2) var(--space-4);
            border-radius: var(--radius-2xl);
            font-size: 0.875rem;
            font-weight: 500;
            margin-bottom: var(--space-6);
        }

        .provider-title {
            font-size: 3rem;
            font-weight: 700;
            line-height: 1.1;
            margin-bottom: var(--space-6);
            letter-spacing: -0.025em;
        }

        .provider-description {
            font-size: 1.25rem;
            color: var(--color-gray-300);
            margin-bottom: var(--space-8);
            line-height: 1.6;
        }

        .provider-benefits {
            display: flex;
            flex-direction: column;
            gap: var(--space-6);
            margin-bottom: var(--space-10);
        }

        .benefit-item {
            display: flex;
            align-items: flex-start;
            gap: var(--space-4);
        }

        .benefit-item i {
            background: rgba(16, 185, 129, 0.1);
            color: var(--color-primary);
            width: 40px;
            height: 40px;
            border-radius: var(--radius-lg);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.1rem;
            margin-top: var(--space-1);
        }

        .benefit-content h4 {
            font-size: 1.1rem;
            font-weight: 600;
            color: var(--color-light);
            margin-bottom: var(--space-2);
        }

        .benefit-content p {
            color: var(--color-gray-300);
            line-height: 1.5;
        }

        .provider-cta {
            display: flex;
            gap: var(--space-4);
            flex-wrap: wrap;
        }

        .analytics-dashboard {
            background: var(--color-light);
            border-radius: var(--radius-2xl);
            padding: var(--space-8);
            color: var(--color-gray-900);
            box-shadow: var(--shadow-2xl);
        }

        .dashboard-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--space-8);
            padding-bottom: var(--space-4);
            border-bottom: 1px solid var(--color-gray-200);
        }

        .dashboard-header h3 {
            font-size: 1.25rem;
            font-weight: 600;
        }

        .model-name {
            background: var(--color-primary);
            color: white;
            padding: var(--space-2) var(--space-4);
            border-radius: var(--radius-lg);
            font-size: 0.875rem;
            font-weight: 500;
        }

        .performance-metrics {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: var(--space-6);
            margin-bottom: var(--space-8);
        }

        .metric {
            background: var(--color-gray-50);
            padding: var(--space-4);
            border-radius: var(--radius-lg);
            text-align: center;
        }

        .metric-label {
            font-size: 0.875rem;
            color: var(--color-gray-500);
            margin-bottom: var(--space-2);
        }

        .metric-value {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--color-gray-900);
            margin-bottom: var(--space-1);
        }

        .metric-trend {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: var(--space-1);
            font-size: 0.875rem;
            color: var(--color-primary);
            font-weight: 500;
        }

        .category-breakdown {
            display: flex;
            flex-direction: column;
            gap: var(--space-4);
        }

        .category-item {
            display: flex;
            align-items: center;
            gap: var(--space-4);
        }

        .category-name {
            font-size: 0.875rem;
            font-weight: 500;
            color: var(--color-gray-700);
            min-width: 100px;
        }

        .category-bar {
            flex: 1;
            height: 8px;
            background: var(--color-gray-200);
            border-radius: var(--radius-sm);
            overflow: hidden;
        }

        .category-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--color-primary), #059669);
            border-radius: var(--radius-sm);
        }

        .category-score {
            font-size: 0.875rem;
            font-weight: 600;
            color: var(--color-gray-700);
            min-width: 40px;
            text-align: right;
        }

        /* Final CTA Section */
        .final-cta {
            padding: var(--space-24) 0;
            background: linear-gradient(135deg, var(--color-dark) 0%, var(--color-dark-lighter) 100%);
            text-align: center;
        }

        .cta-title {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: var(--space-6);
            letter-spacing: -0.025em;
        }

        .cta-description {
            font-size: 1.25rem;
            color: var(--color-gray-300);
            max-width: 600px;
            margin: 0 auto var(--space-12);
            line-height: 1.6;
        }

        .cta-options {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: var(--space-8);
            max-width: 800px;
            margin: 0 auto var(--space-12);
        }

        .cta-option {
            background: var(--color-light);
            border-radius: var(--radius-2xl);
            padding: var(--space-8);
            color: var(--color-gray-900);
            box-shadow: var(--shadow-lg);
        }

        .option-icon {
            background: linear-gradient(135deg, var(--color-primary), #059669);
            color: white;
            width: 60px;
            height: 60px;
            border-radius: var(--radius-xl);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            margin: 0 auto var(--space-6);
        }

        .cta-option h3 {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: var(--space-4);
            color: var(--color-gray-900);
        }

        .cta-option p {
            color: var(--color-gray-600);
            margin-bottom: var(--space-6);
            line-height: 1.5;
        }

        .trust-indicators {
            display: flex;
            justify-content: center;
            gap: var(--space-8);
            flex-wrap: wrap;
        }

        .trust-item {
            display: flex;
            align-items: center;
            gap: var(--space-2);
            color: var(--color-gray-300);
            font-size: 0.875rem;
            font-weight: 500;
        }

        .trust-item i {
            color: var(--color-primary);
        }

        /* Footer */
        .footer {
            background: var(--color-gray-900);
            padding: var(--space-16) 0 var(--space-8);
            border-top: 1px solid var(--color-gray-800);
        }

        .footer-content {
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: var(--space-16);
            margin-bottom: var(--space-12);
        }

        .footer-logo {
            font-size: 1.75rem;
            font-weight: 700;
            color: var(--color-primary);
            text-decoration: none;
            margin-bottom: var(--space-4);
            display: block;
        }

        .footer-description {
            color: var(--color-gray-400);
            line-height: 1.6;
            margin-bottom: var(--space-6);
        }

        .social-links {
            display: flex;
            gap: var(--space-4);
        }

        .social-link {
            background: var(--color-gray-800);
            color: var(--color-gray-400);
            width: 40px;
            height: 40px;
            border-radius: var(--radius-lg);
            display: flex;
            align-items: center;
            justify-content: center;
            text-decoration: none;
            transition: all 0.2s ease;
        }

        .social-link:hover {
            background: var(--color-primary);
            color: white;
        }

        .footer-links {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: var(--space-8);
        }

        .link-group h4 {
            font-size: 1rem;
            font-weight: 600;
            color: var(--color-light);
            margin-bottom: var(--space-4);
        }

        .link-group a {
            display: block;
            color: var(--color-gray-400);
            text-decoration: none;
            margin-bottom: var(--space-3);
            transition: color 0.2s ease;
        }

        .link-group a:hover {
            color: var(--color-primary);
        }

        .footer-bottom {
            text-align: center;
            padding-top: var(--space-8);
            border-top: 1px solid var(--color-gray-800);
            color: var(--color-gray-500);
        }

        .footer-bottom p {
            margin-bottom: var(--space-2);
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .impact-grid,
            .provider-content {
                grid-template-columns: 1fr;
                gap: var(--space-12);
            }

            .impact-stats {
                grid-template-columns: 1fr;
            }

            .cta-options {
                grid-template-columns: 1fr;
            }

            .footer-content {
                grid-template-columns: 1fr;
                gap: var(--space-12);
            }

            .footer-links {
                grid-template-columns: repeat(2, 1fr);
                gap: var(--space-6);
            }

            .trust-indicators {
                gap: var(--space-4);
            }

            .cta-title,
            .provider-title {
                font-size: 2.5rem;
            }
        }
    </style>

    <!-- JavaScript for Interactivity -->
    <script>
        // Mobile menu toggle
        document.addEventListener('DOMContentLoaded', function() {
            const mobileMenuBtn = document.querySelector('.mobile-menu-btn');
            const navLinks = document.querySelector('.nav-links');

            if (mobileMenuBtn) {
                mobileMenuBtn.addEventListener('click', function() {
                    navLinks.style.display = navLinks.style.display === 'flex' ? 'none' : 'flex';
                });
            }

            // Smooth scrolling for anchor links
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });

            // Filter buttons functionality
            const filterBtns = document.querySelectorAll('.filter-btn');
            filterBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    filterBtns.forEach(b => b.classList.remove('active'));
                    this.classList.add('active');
                });
            });

            // Add scroll effect to navbar
            window.addEventListener('scroll', function() {
                const navbar = document.querySelector('.navbar');
                if (window.scrollY > 100) {
                    navbar.style.background = 'rgba(15, 23, 42, 0.98)';
                } else {
                    navbar.style.background = 'rgba(15, 23, 42, 0.95)';
                }
            });

            // Animated number counting
            function animateNumbers() {
                const numbers = document.querySelectorAll('.stat-number');
                numbers.forEach(number => {
                    const target = parseInt(number.textContent.replace(/,/g, ''));
                    let current = 0;
                    const increment = target / 100;
                    const timer = setInterval(() => {
                        current += increment;
                        if (current >= target) {
                            current = target;
                            clearInterval(timer);
                        }
                        number.textContent = Math.floor(current).toLocaleString();
                    }, 20);
                });
            }

            // Intersection Observer for animations
            const observerOptions = {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            };

            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.animationPlayState = 'running';
                        if (entry.target.classList.contains('hero-stats')) {
                            setTimeout(animateNumbers, 1000);
                        }
                    }
                });
            }, observerOptions);

            // Observe elements for animation
            document.querySelectorAll('.step-card, .hero-stats, .section-header').forEach(el => {
                observer.observe(el);
            });

            // Parallax effect for floating elements
            window.addEventListener('scroll', function() {
                const scrolled = window.pageYOffset;
                const parallax = document.querySelectorAll('.floating-element');
                const speed = 0.5;

                parallax.forEach(element => {
                    const yPos = -(scrolled * speed);
                    element.style.transform = `translateY(${yPos}px)`;
                });
            });

            // Micro-interactions for buttons
            document.querySelectorAll('.btn').forEach(btn => {
                btn.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-2px) scale(1.02)';
                });

                btn.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });

            // Card hover effects
            document.querySelectorAll('.step-card, .category-card, .stat-card').forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-8px) scale(1.02)';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });
        });
    </script>
</body>
</html>