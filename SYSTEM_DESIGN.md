# Huval - AI Evaluation Platform System Design

## Project Overview
Huval is a revolutionary AI evaluation platform that breaks away from traditional dataset-based evaluation methods. Built on forum principles, it allows users to create topics, submit questions to multiple AI systems, and evaluate responses through community voting. The platform features a minimalist, modern design inspired by BentoUI principles with card-based layouts and sophisticated user experience.

## Platform Concept
Unlike existing AI evaluation platforms that rely on datasets and are influenced by model creators, <PERSON><PERSON> uses real user questions and community-driven evaluation to provide unbiased AI performance metrics through a global leaderboard system.

## Design Inspiration Analysis (Visual Reference)

### Visual Design Principles (Adapted from Reference)
- **Color Scheme**: Dark theme with clean light cards
- **Primary Colors**:
  - Background: Dark (#1a1a1a or similar)
  - Cards: Clean white (#ffffff)
  - Accent: Emerald green (#10B981) for AI performance indicators
  - Text: High contrast typography
- **Layout**: Card-based BentoUI design with rounded corners
- **Typography**: Clean, modern sans-serif fonts
- **Spacing**: Generous whitespace and strategic padding

### Key UI Components (Adapted for AI Platform)
1. **Navigation Bar**
   - Brand logo "huval"
   - Horizontal navigation (max 8 elements)
   - Login/Register CTAs
   - User profile access

2. **Hero Section**
   - Compelling headline about AI evaluation
   - Key platform statistics
   - Call-to-action for topic creation
   - Visual elements showcasing AI comparison

3. **Interactive Cards**
   - Topic creation cards
   - AI response comparison cards
   - Voting interface cards
   - Leaderboard display cards

4. **Forum-Style Layout**
   - Topic listings with categories
   - Response threads
   - Voting mechanisms
   - User engagement metrics

## System Architecture

### Pure HTML Template Architecture
```
┌─────────────────────────────────────────┐
│           Static HTML Templates        │
├─────────────────────────────────────────┤
│  • Self-contained HTML5 pages          │
│  • Shared CSS stylesheet               │
│  • Vanilla JavaScript functionality    │
│  • No build process required           │
│  • Direct browser compatibility        │
│  • BentoUI design principles           │
└─────────────────────────────────────────┘
```

### Technology Stack
- **HTML**: Pure HTML5 with semantic markup
- **CSS**: Single stylesheet with CSS Grid/Flexbox
- **JavaScript**: Vanilla JS for interactivity (no frameworks)
- **Icons**: Inline SVG or icon fonts
- **Images**: Optimized web formats (WebP with fallbacks)
- **No Build Tools**: Direct browser-ready files

### Project Structure (Pure HTML)
```
huval/
├── index.html                 # Landing page (7+ sections)
├── leaderboard.html          # Global AI leaderboard
├── topics.html               # Topic listings
├── create-topic.html         # Topic creation form
├── topic-detail.html         # Individual topic view
├── profile.html              # User profile page
├── login.html                # Login form
├── register.html             # Registration form
├── about.html                # About page
├── admin-dashboard.html      # Admin dashboard
├── ai-providers.html         # AI provider management
├── moderation.html           # Content moderation
├── assets/
│   ├── css/
│   │   └── style.css         # Single main stylesheet
│   ├── js/
│   │   └── main.js           # Single JavaScript file
│   ├── images/
│   │   ├── hero-bg.jpg       # Hero background
│   │   ├── logo.svg          # Platform logo
│   │   ├── icons/            # UI icons
│   │   └── avatars/          # User avatars
│   └── data/
│       ├── sample-topics.js  # Mock data for demos
│       ├── sample-ais.js     # Sample AI data
│       └── sample-users.js   # Sample user data
├── SYSTEM_DESIGN.md          # This document
└── README.md                 # Project documentation
```

### HTML Template Approach
- **Self-Contained Pages**: Each HTML file includes complete structure
- **Shared Resources**: Common CSS and JS files linked across pages
- **No Templating**: Pure HTML without preprocessing
- **Static Navigation**: Navigation menu coded in each page
- **Mock Data**: JavaScript objects for demonstration purposes
- **Progressive Enhancement**: Core functionality works without JS

## Core Features & User Roles

### 1. User Account Types
- **Super Admin**: Full platform control, AI provider management
- **Admin Support**: Moderation and user support
- **Simple Users**: Topic creation, voting, evaluation
- **AI Provider**: Model listing, performance analytics

### 2. Topic Management System
- **Topic Creation**: Category selection, NSFW flagging
- **Categories**: Coding, General Knowledge, QA, etc.
- **AI Response Collection**: Batched API requests
- **Anonymous Response Display**: Forum-style presentation

### 3. Evaluation & Voting System
- **Community Voting**: User-driven AI response evaluation
- **Rate Limiting**: Prevents spam and manipulation
- **Performance Metrics**: Comprehensive AI scoring
- **Global Leaderboard**: Real-time AI rankings

## Landing Page Structure (7+ Sections Required)

### 1. Hero Section
- **Headline**: "Revolutionizing AI Evaluation Through Community"
- **Subheadline**: Platform mission and unique approach
- **CTA Buttons**: "Start Evaluating" and "View Leaderboard"
- **Visual**: AI comparison graphics

### 2. How It Works
- **Step-by-step process**: Topic creation → AI responses → Community voting
- **Interactive cards**: Each step with visual representation
- **Benefits**: Why community evaluation is superior

### 3. AI Leaderboard Preview
- **Top performing AIs**: Live leaderboard snippet
- **Performance metrics**: Accuracy, helpfulness, creativity scores
- **CTA**: "View Full Leaderboard"

### 4. Categories Showcase
- **Topic categories**: Coding, General Knowledge, QA, Creative Writing
- **Visual grid**: Category cards with icons and descriptions
- **Statistics**: Number of topics per category

### 5. Community Impact
- **User statistics**: Active evaluators, topics created, responses evaluated
- **Testimonials**: User feedback and success stories
- **Trust indicators**: Platform credibility elements

### 6. For AI Providers
- **Provider benefits**: Performance insights, model improvement data
- **Integration process**: How to add AI models to platform
- **Success metrics**: ROI and improvement tracking

### 7. Call to Action
- **Join the community**: Registration encouragement
- **Multiple entry points**: Different user types (evaluators, providers)
- **Social proof**: Recent activity and community growth

## Technical Specifications

### CSS Architecture (BentoUI System)
```css
:root {
  /* Color System */
  --color-primary: #10B981;      /* Emerald green */
  --color-secondary: #6366F1;    /* Indigo */
  --color-accent: #F59E0B;       /* Amber */
  --color-dark: #1a1a1a;         /* Dark background */
  --color-light: #ffffff;        /* Light cards */
  --color-gray-50: #F9FAFB;
  --color-gray-100: #F3F4F6;
  --color-gray-900: #111827;

  /* Typography */
  --font-primary: 'Inter', system-ui, sans-serif;
  --font-mono: 'JetBrains Mono', monospace;

  /* Spacing System */
  --space-1: 0.25rem;   /* 4px */
  --space-2: 0.5rem;    /* 8px */
  --space-3: 0.75rem;   /* 12px */
  --space-4: 1rem;      /* 16px */
  --space-6: 1.5rem;    /* 24px */
  --space-8: 2rem;      /* 32px */
  --space-12: 3rem;     /* 48px */
  --space-16: 4rem;     /* 64px */

  /* Border Radius */
  --radius-sm: 0.375rem;  /* 6px */
  --radius-md: 0.5rem;    /* 8px */
  --radius-lg: 0.75rem;   /* 12px */
  --radius-xl: 1rem;      /* 16px */
  --radius-2xl: 1.5rem;   /* 24px */

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1);
}
```

### JavaScript Functionality (Single File Approach)
```javascript
// main.js - All functionality in one file with clear sections

// 1. Navigation & UI Interactions
// - Mobile menu toggle
// - Page transitions
// - Modal dialogs
// - Form interactions

// 2. Mock Data Management
// - Load sample topics, AIs, users
// - Simulate API responses
// - Local storage for demo persistence

// 3. Voting System Simulation
// - Vote button interactions
// - Vote count updates
// - Visual feedback
// - Rate limiting simulation

// 4. Search & Filtering
// - Topic search functionality
// - Category filtering
// - Leaderboard sorting
// - Results display

// 5. Form Handling
// - Topic creation form
// - Login/register forms
// - Form validation
// - Success/error messages

// 6. Dynamic Content
// - Leaderboard updates
// - Topic list rendering
// - User profile display
// - Admin panel interactions
```

### Navigation Structure (Max 8 Elements)
1. **huval** (Logo/Home)
2. **Leaderboard** (Global AI rankings)
3. **Topics** (Browse/Create topics)
4. **Categories** (Topic categories)
5. **Community** (User engagement)
6. **About** (Platform information)
7. **Sign In** (Authentication)
8. **Join Now** (Registration CTA)

## Development Phases (Pure HTML Approach)

### Phase 1: HTML Structure
- **Landing page** (index.html) with 7+ required sections
- **Core pages**: leaderboard.html, topics.html, create-topic.html
- **User pages**: login.html, register.html, profile.html
- **Admin pages**: admin-dashboard.html, moderation.html
- **Semantic HTML5** markup throughout

### Phase 2: CSS Styling (Single File)
- **CSS variables** for consistent theming
- **BentoUI card-based** design system
- **Dark theme** with light card overlays
- **Responsive grid** layouts
- **Typography** and spacing system

### Phase 3: JavaScript Functionality
- **Mock data** integration for demonstrations
- **Interactive elements**: voting, forms, navigation
- **Search and filtering** functionality
- **Dynamic content** updates
- **Form validation** and user feedback

### Phase 4: Polish & Optimization
- **Cross-browser** compatibility testing
- **Performance** optimization
- **Accessibility** improvements (WCAG 2.1)
- **Mobile responsiveness** refinement

## Quality Standards

### Design Principles
- **Minimalist**: Clean, uncluttered interface
- **Modern**: 2025 web design standards
- **Accessible**: WCAG 2.1 AA compliance
- **Responsive**: Mobile-first approach
- **Performance**: Fast loading, optimized assets

### Code Standards (Pure HTML)
- **HTML**: Semantic HTML5, valid markup, accessibility attributes
- **CSS**: Organized sections, consistent naming, mobile-first
- **JavaScript**: Vanilla JS, clear functions, inline documentation
- **Files**: Self-contained pages, shared assets, no build process

## Implementation Notes

### Mock Data Strategy
Since this is a pure HTML template system, all dynamic content will be simulated using:
- **JavaScript objects** containing sample topics, AI responses, user data
- **Local storage** for maintaining demo state between pages
- **Simulated interactions** for voting, topic creation, user management

### Page Interconnectivity
- **Consistent navigation** manually coded in each HTML file
- **Shared styling** through single CSS file
- **Cross-page functionality** through shared JavaScript
- **URL parameters** for passing data between pages (e.g., topic IDs)

### Responsive Design Approach
- **Mobile-first** CSS with progressive enhancement
- **Flexible grid** systems using CSS Grid and Flexbox
- **Breakpoint strategy**: Mobile (320px+), Tablet (768px+), Desktop (1024px+)
- **Touch-friendly** interface elements for mobile devices

This system design provides the foundation for building Huval as a collection of pure HTML templates that demonstrate a revolutionary AI evaluation platform with sophisticated design and user experience, ready for direct browser viewing without any build process.
